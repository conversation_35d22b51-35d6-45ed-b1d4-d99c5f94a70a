from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocket<PERSON><PERSON>nnect, <PERSON><PERSON>, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import os
from datetime import datetime
from typing import Optional
from services import AgentService
from config import get_settings


# --------------------------------------------------------------------------
# INITIALIZATION
# --------------------------------------------------------------------------

settings = get_settings()
agent_service = AgentService(settings)

app = FastAPI(title="Continuia Health - Arika AI", version="1.0.0")
logger = logging.getLogger("app")
logging.basicConfig(level=logging.INFO)

# CORS configuration - specify actual frontend origins when using credentials
allowed_origins = [
    "http://localhost:3000",      # Local development
    "http://localhost:3001",      # Alternative local port
    "http://localhost:5173",      # Vite default
    "http://localhost:8080",      # Common dev port
    "http://127.0.0.1:3000",      # Alternative localhost
    "http://127.0.0.1:3001",
    "http://127.0.0.1:5173",
    "http://127.0.0.1:8080",
    "https://raccha.ai",          # Production domain
    "https://www.raccha.ai",      # Production with www
    "https://app.raccha.ai",      # Production app subdomain
    "https://dev--continuia-web.netlify.app",  # Netlify dev deployment
    "https://continuia-web.netlify.app",        # Netlify main deployment
    "https://*.netlify.app",      # All Netlify preview deployments
    "https://dev-letta.raccha.ai", # Dev Letta server
    # Add your actual frontend URLs here
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,  # Specific origins required when credentials=True
    allow_credentials=True,          # Allow cookies
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],            # Expose all headers to the client
)


# --------------------------------------------------------------------------
# CORE ENDPOINTS (KEEP)
# --------------------------------------------------------------------------

@app.get("/agents/{agent_name}")
async def create_session_by_agent_name(agent_name: str, category: str = "health_care"):
    """Create a new session - returns session_id in response AND sets it as a cookie"""
    try:
        result = await agent_service.create_session(category, agent_name)
        
        logger.info(f"[DEBUG Create] Created session {result['sessionId']} for agent {agent_name}")
        
        # Create response with session data
        response = JSONResponse(content={
            "session_id": result["sessionId"]
        })
        
        # ALSO set the session_id as a cookie for automatic inclusion in future requests
        # This provides dual functionality: explicit (response) and automatic (cookie)
        response.set_cookie(
            key="session_id",
            value=result["sessionId"],
            httponly=False,       # Allow JavaScript access for frontend frameworks
            samesite="none",      # Allow cross-site cookies (frontend and backend on different domains/ports)
            max_age=86400,        # 24 hours expiry (in seconds)
            path="/",             # Available for all paths
            secure=True           # Required when samesite="none" (use False for local non-HTTPS development)
        )
        
        logger.info(f"[DEBUG Create] Set session_id cookie: {result['sessionId']}")
        
        return response
        
    except Exception as e:
        logger.error(f"Session creation error for agent {agent_name}: {e}")
        return JSONResponse(status_code=500, content={
            "error": "Failed to create session",
            "detail": str(e)
        })


@app.websocket("/agents/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    agent_name: str,
    session_id: Optional[str] = Query(None, description="Session ID for WebSocket connection")
):
    """WebSocket endpoint for real-time communication with agents - accepts session_id from cookie or query param"""
    
    # DEBUG: Log all received cookies
    logger.info(f"[DEBUG WebSocket] All cookies received: {websocket.cookies}")
    logger.info(f"[DEBUG WebSocket] Query session_id: {session_id}")
    logger.info(f"[DEBUG WebSocket] Agent name: {agent_name}")
    
    # Try to get session_id from cookie first, then query parameter
    cookie_session_id = websocket.cookies.get("session_id")
    actual_session_id = cookie_session_id or session_id
    
    logger.info(f"[DEBUG WebSocket] Cookie session_id: {cookie_session_id}")
    logger.info(f"[DEBUG WebSocket] Final session_id to use: {actual_session_id}")
    
    # Handle undefined or missing session_id
    if not actual_session_id or actual_session_id == "undefined" or actual_session_id == "null":
        await websocket.close(code=1008, reason="Valid session ID required")
        logger.error(f"[DEBUG WebSocket] Connection rejected - Query: '{session_id}', Cookie: '{cookie_session_id}'")
        return
    
    # Validate session exists
    if actual_session_id not in agent_service.session_data:
        await websocket.close(code=1008, reason="Session not found")
        logger.error(f"WebSocket connection rejected - Session not found: {actual_session_id}")
        return
    
    await websocket.accept()
    logger.info(f"WebSocket connected - Session: {actual_session_id}, Agent: {agent_name}")
    
    try:
        await agent_service.handle_websocket(websocket, actual_session_id, agent_name)
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected - Session: {actual_session_id}")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")


@app.get("/agents/{agent_name}/messages")
async def get_agent_messages(
    agent_name: str,
    session_id_cookie: Optional[str] = Cookie(None, alias="session_id"),  # Accept from cookie
    session_id: Optional[str] = Query(None, description="Session ID for message retrieval"),  # Accept from query
    all: bool = True,
    batch: int = 200,
    include_welcome: bool = True,
    bare: bool = True
):
    """Get messages for an agent - accepts session_id from cookie OR query param"""
    try:
        # DEBUG: Log what we received
        logger.info(f"[DEBUG Messages] Cookie session_id: {session_id_cookie}")
        logger.info(f"[DEBUG Messages] Query session_id: {session_id}")
        
        # Accept session_id from either cookie or query parameter (cookie takes precedence)
        session_id = session_id_cookie or session_id
        logger.info(f"[DEBUG Messages] Final session_id to use: {session_id}")
        
        if not session_id or session_id == "undefined":
            # No session provided - try to find by agent name (backward compat)
            logger.warning(f"[DEBUG] No session ID provided for agent: {agent_name}")
            mapped_agent_name = agent_service.settings.get_template_agent_name(agent_name)
            matching_sessions = [
                sid for sid, data in agent_service.session_data.items()
                if data.get("agent_type") == mapped_agent_name
            ]
            
            if matching_sessions:
                session_id = matching_sessions[-1]
                logger.warning(f"[DEBUG] Using last matching session: {session_id}")
            else:
                logger.warning(f"[DEBUG] No sessions found for agent {agent_name}")
                return {"messages": []}
        
        # Validate session exists
        session = agent_service.session_data.get(session_id)
        if not session:
            logger.warning(f"[DEBUG] Session {session_id} not found in session_data")
            return JSONResponse(status_code=404, content={
                "error": "Session not found",
                "messages": []
            })
        
        logger.info(f"[DEBUG] Session found: {session['agent_type']}, agent_id: {session['agent_id']}")
        
        result = agent_service.get_agent_messages(session_id, include_welcome)
        logger.info(f"[DEBUG] Retrieved {len(result)} messages")
        
        return {"messages": result}
        
    except Exception as e:
        logger.error(f"Error getting agent messages: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"messages": []}


@app.get("/health")
async def health_check():
    """Health check endpoint with system status"""
    return {
        "status": "healthy",
        "templates_loaded": len(agent_service.loaded_templates),
        "active_sessions": len(agent_service.session_data),
        "categories": list(agent_service.agent_categories.keys()),
        "total_categories": len(agent_service.agent_categories),
        "custom_tools": list(agent_service.custom_tools.keys()),
        "folders_base_dir": settings.FOLDERS_BASE_DIR,
        "server_time": datetime.utcnow().isoformat()
    }


@app.get("/agents/{agent_name}/restore-messages")
async def restore_messages(
    agent_name: str,
    session_id_cookie: Optional[str] = Cookie(None, alias="session_id"),  # Accept from cookie
    session_id: Optional[str] = Query(None, description="Session ID to restore messages for"),  # Accept from query
    include_welcome: bool = Query(True, description="Include welcome message")
):
    """
    Restore all messages for a session - accepts session_id from cookie OR query param.
    
    Options:
    1. Cookie: Cookie header with session_id=your_session_id
    2. Query: URL parameter ?session_id=your_session_id
    
    Example: http://localhost:3000/agents/arika_reddy/restore-messages?session_id=your_session_id
    """
    try:
        # DEBUG: Log what we received
        logger.info(f"[DEBUG Restore] Cookie session_id: {session_id_cookie}")
        logger.info(f"[DEBUG Restore] Query session_id: {session_id}")
        
        # Accept session_id from either cookie or query parameter (cookie takes precedence)
        session_id = session_id_cookie or session_id
        logger.info(f"[DEBUG Restore] Final session_id to use: {session_id}")
        
        if not session_id:
            return JSONResponse(status_code=400, content={
                "error": "Session ID required",
                "messages": [],
                "help": "Provide session_id via Cookie header OR query param: ?session_id=your_session_id"
            })
        
        # Verify session exists
        if session_id not in agent_service.session_data:
            return JSONResponse(status_code=404, content={
                "error": "Session not found",
                "messages": [],
                "help": "Session may have expired. Create a new session."
            })
        
        logger.info(f"[RestoreMessages] Restoring messages for session {session_id} via cookie")
        
        # Verify session exists
        if session_id not in agent_service.session_data:
            return JSONResponse(status_code=404, content={
                "error": "Session not found",
                "messages": []
            })
        
        # Get the agent_id for this session (ensures isolation)
        agent_id = agent_service.get_agent_id_for_session(session_id)
        if not agent_id:
            return JSONResponse(status_code=404, content={
                "error": "Agent not found for session",
                "messages": []
            })
        
        logger.info(f"[RestoreMessages] Found agent {agent_id} for session {session_id} - using get_agent_messages")
        
        # Use the existing get_agent_messages method which has the proper format
        # This includes the welcome message and proper formatting
        messages = agent_service.get_agent_messages(session_id, include_welcome)
        
        logger.info(f"[RestoreMessages] Restored {len(messages)} messages for session {session_id}")
        
        # Return in the exact same format as the messages endpoint
        return {
            "messages": messages
        }
        
    except Exception as e:
        logger.error(f"[RestoreMessages] Error: {e}")
        import traceback
        logger.error(f"[RestoreMessages] Traceback: {traceback.format_exc()}")
        return JSONResponse(status_code=500, content={
            "error": "Failed to restore messages",
            "messages": []
        })


@app.get("/agents/{agent_name}/sessions/{session_id}/messages")
async def get_messages_by_session_id(
    agent_name: str,
    session_id: str,
    include_welcome: bool = Query(True, description="Include welcome message")
):
    """
    Get messages for a specific session using session ID in the URL path.
    
    Clean URL Example: http://localhost:3000/agents/arika_reddy/sessions/ab761da/messages
    
    This is an alternative to using cookies - useful for direct access or testing.
    """
    try:
        # Verify session exists
        if session_id not in agent_service.session_data:
            return JSONResponse(status_code=404, content={
                "error": "Session not found",
                "messages": [],
                "session_id": session_id
            })
        
        # Verify agent name matches session
        session = agent_service.session_data[session_id]
        if session.get("agent_type") != agent_service.settings.get_template_agent_name(agent_name):
            logger.warning(f"Agent name mismatch for session {session_id}")
        
        logger.info(f"[GetMessages] Getting messages for session {session_id} via URL path")
        
        # Get messages using the existing method
        messages = agent_service.get_agent_messages(session_id, include_welcome)
        
        logger.info(f"[GetMessages] Retrieved {len(messages)} messages for session {session_id}")
        
        return {
            "messages": messages,
            "session_id": session_id,
            "agent": agent_name
        }
        
    except Exception as e:
        logger.error(f"[GetMessages] Error: {e}")
        return JSONResponse(status_code=500, content={
            "error": "Failed to get messages",
            "messages": []
        })


@app.post("/agents/create-post-otp")
async def create_post_otp_agents(
    user_phone: str = Query(..., description="User's verified phone number"),
    user_name: str = Query(..., description="User's name"),
    session_id: str = Query(..., description="Original session ID from intake"),
    chief_complaint: str = Query("General consultation", description="Primary reason for consultation")
):
    """
    Create user and case agents after successful OTP verification.
    This is called when frontend retrieves user info from scratch memory after OTP success.
    
    Creates:
    1. arika_reddy_user agent - Personal user agent with phone number (interacts with user)
    2. arika_reddy_case agent - Backend case management agent (stores case data only, no user interaction)
    """
    try:
        import uuid
        from datetime import datetime
        
        # Validate inputs
        if not user_phone or not user_name:
            return JSONResponse(status_code=400, content={
                "error": "Phone number and name are required",
                "success": False
            })
        
        # Clean phone number (remove any spaces or special chars except +)
        user_phone_clean = user_phone.replace(" ", "").replace("-", "")
        
        # Generate unique IDs
        case_id = uuid.uuid4().hex[:12]
        user_agent_id = f"user_{uuid.uuid4().hex[:8]}"
        timestamp = datetime.utcnow().isoformat()
        
        logger.info(f"[CreatePostOTP] Creating agents for user: {user_name}, phone: {user_phone_clean}")
        
        # Create user agent (this one interacts with the user)
        user_agent_result = await agent_service.create_agent_from_template_with_vars(
            template_name="health_care_arika_reddy_user",
            variables={
                "user_phone": user_phone_clean.replace('+91', ''),  # Remove country code for agent name
                "user_name": user_name,
                "session_id": session_id,
                "case_id": case_id,
                "created_timestamp": timestamp,
                "agent_id": user_agent_id
            }
        )
        
        if not user_agent_result.get("success"):
            logger.error(f"[CreatePostOTP] Failed to create user agent: {user_agent_result.get('error')}")
            return JSONResponse(status_code=500, content={
                "error": "Failed to create user agent",
                "details": user_agent_result.get("error"),
                "success": False
            })
        
        logger.info(f"[CreatePostOTP] User agent created: {user_agent_result.get('agent_id')}")
        
        # Create case agent (backend only - stores case data, no user interaction)
        case_agent_result = await agent_service.create_agent_from_template_with_vars(
            template_name="health_care_arika_reddy_case",
            variables={
                "case_id": case_id,
                "user_phone": user_phone_clean,
                "user_name": user_name,
                "user_agent_id": user_agent_result.get("agent_id"),
                "chief_complaint": chief_complaint,
                "created_timestamp": timestamp
            }
        )
        
        if not case_agent_result.get("success"):
            logger.error(f"[CreatePostOTP] Failed to create case agent: {case_agent_result.get('error')}")
            # Clean up user agent if case agent creation fails
            try:
                agent_service.client.agents.delete(agent_id=user_agent_result.get("agent_id"))
            except:
                pass
            
            return JSONResponse(status_code=500, content={
                "error": "Failed to create case agent",
                "details": case_agent_result.get("error"),
                "success": False
            })
        
        logger.info(f"[CreatePostOTP] Case agent created: {case_agent_result.get('agent_id')}")
        
        # Return success with both agent details
        return {
            "success": True,
            "message": "Both agents created successfully",
            "user_agent": {
                "agent_id": user_agent_result.get("agent_id"),
                "agent_name": user_agent_result.get("agent_name"),
                "purpose": "User interaction and personal healthcare assistant"
            },
            "case_agent": {
                "agent_id": case_agent_result.get("agent_id"),
                "agent_name": case_agent_result.get("agent_name"),
                "case_id": case_id,
                "purpose": "Backend case data storage (no user interaction)"
            },
            "metadata": {
                "user_name": user_name,
                "user_phone": user_phone_clean,
                "original_session_id": session_id,
                "created_at": timestamp
            }
        }
        
    except Exception as e:
        logger.error(f"[CreatePostOTP] Error creating agents: {e}")
        import traceback
        logger.error(f"[CreatePostOTP] Traceback: {traceback.format_exc()}")
        return JSONResponse(status_code=500, content={
            "error": "Internal server error",
            "details": str(e),
            "success": False
        })


@app.delete("/sessions")
async def delete_session(
    session_id: str = Query(..., description="Session ID to delete")
):
    """Delete a session using session_id from query parameter"""
    try:
        # Session ID is now required and comes only from query parameter
        
        if not session_id:
            return JSONResponse(status_code=400, content={
                "error": "Session ID required as query parameter"
            })
        
        logger.info(f"Deleting session {session_id}")
        
        result = agent_service.delete_session(session_id)
        
        if result.get("success"):
            return {
                "success": True,
                "message": f"Session {session_id} deleted successfully"
            }
        else:
            return JSONResponse(status_code=404, content={
                "error": result.get("error", "Failed to delete session")
            })
            
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        return JSONResponse(status_code=500, content={
            "error": "Internal server error"
        })


@app.get("/sessions/debug")
async def debug_sessions():
    """Debug endpoint to see all active sessions"""
    try:
        sessions_info = []
        for session_id, session_data in agent_service.session_data.items():
            sessions_info.append({
                "session_id": session_id,
                "agent_id": session_data.get("agent_id", "unknown")[:20] + "...",
                "category": session_data.get("category"),
                "agent_type": session_data.get("agent_type"),
                "created_at": session_data.get("created_at", "unknown")
            })
        
        return {
            "total_sessions": len(agent_service.session_data),
            "sessions": sessions_info
        }
    except Exception as e:
        logger.error(f"Debug sessions error: {e}")
        return {"error": str(e)}


# --------------------------------------------------------------------------
# STARTUP EVENT
# --------------------------------------------------------------------------

@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup"""
    logger.info("=" * 60)
    logger.info("Starting Continuia Health - Arika AI Server")
    logger.info("=" * 60)
    logger.info(f"Server Version: 1.0.0")
    logger.info(f"Letta Base URL: {settings.LETTA_BASE_URL}")
    logger.info(f"Templates Directory: {settings.TEMPLATES_DIR}")
    logger.info(f"Folders Base Directory: {settings.FOLDERS_BASE_DIR}")
    logger.info(f"Loaded {len(agent_service.loaded_templates)} templates")
    logger.info(f"Available categories: {list(agent_service.agent_categories.keys())}")
    logger.info(f"Custom tools registered: {list(agent_service.custom_tools.keys())}")
    logger.info("=" * 60)
    logger.info("Server ready to accept connections")
    logger.info("=" * 60)


# --------------------------------------------------------------------------
# MAIN
# --------------------------------------------------------------------------

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app, 
        host=settings.HOST, 
        port=settings.PORT,
        log_level="info",
        reload=False
    )
