from letta_client import Letta

# Connect to remote Letta server
client = Letta(base_url="https://letta.raccha.ai", token="product-raccha.ai")

def list_all_agents():
    """List all existing agents"""
    try:
        agents = client.agents.list()
        print(f"✅ Connected successfully! Found {len(agents)} agents.")
        print(f"Server URL: https://letta.raccha.ai")
        print("-" * 50)
        
        if agents:
            print("Existing agents:")
            for i, agent in enumerate(agents, 1):
                print(f"{i}. Name: {agent.name}")
                print(f"   ID: {agent.id}")
                print(f"   Description: {agent.description}")
                print(f"   Created: {agent.created_at}")
                print("-" * 30)
        else:
            print("No agents found.")
        
        return agents
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return None

def delete_all_agents():
    """Delete all existing agents"""
    try:
        agents = client.agents.list()
        
        if not agents:
            print("No agents to delete.")
            return
        
        print(f"Found {len(agents)} agents to delete...")
        
        for agent in agents:
            try:
                print(f"Deleting agent: {agent.name} ({agent.id})")
                client.agents.delete(agent.id)
                print(f"✅ Successfully deleted: {agent.name}")
            except Exception as e:
                print(f"❌ Failed to delete {agent.name}: {str(e)}")
        
        print(f"\n🗑️ Deletion process completed!")
        
        # Verify deletion
        remaining_agents = client.agents.list()
        print(f"Remaining agents: {len(remaining_agents)}")
        
    except Exception as e:
        print(f"❌ Error during deletion: {str(e)}")

def delete_specific_agent(agent_id):
    """Delete a specific agent by ID"""
    try:
        client.agents.delete(agent_id)
        print(f"✅ Successfully deleted agent: {agent_id}")
    except Exception as e:
        print(f"❌ Failed to delete agent {agent_id}: {str(e)}")

if __name__ == "__main__":
    print("🔗 Connecting to Letta.raccha.ai...")
    print("=" * 60)
    
    # List all agents first
    agents = list_all_agents()
    
    if agents is None:
        print("Cannot proceed - connection failed.")
        exit(1)
    
    if agents:
        print("\n" + "=" * 60)
        response = input("Do you want to DELETE ALL agents? (yes/no): ").lower().strip()
        
        if response in ['yes', 'y']:
            print("\n🗑️ Starting deletion process...")
            delete_all_agents()
        else:
            print("Deletion cancelled.")
            
            # Option to delete specific agents
            print("\nAlternatively, you can delete specific agents:")
            for i, agent in enumerate(agents, 1):
                print(f"{i}. {agent.name} ({agent.id})")
            
            try:
                choice = input("\nEnter agent number to delete (or 'skip'): ").strip()
                if choice.isdigit():
                    agent_index = int(choice) - 1
                    if 0 <= agent_index < len(agents):
                        agent_to_delete = agents[agent_index]
                        confirm = input(f"Delete '{agent_to_delete.name}'? (yes/no): ").lower().strip()
                        if confirm in ['yes', 'y']:
                            delete_specific_agent(agent_to_delete.id)
            except:
                print("Invalid selection.")
    
    print("\n✅ Script completed!")