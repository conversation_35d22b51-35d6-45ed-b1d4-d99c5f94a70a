import yaml
import os
import uuid
import logging
import stat
import json
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
from letta_client import Letta
from fastapi import WebSocket
import sys
import inspect
import textwrap
sys.path.append("/app/data")



class AgentService:
    """Generic service class for all agent, template, and session management"""
    
    def __init__(self, settings):
        self.settings = settings
        self.client = Letta(
            base_url=settings.LETTA_BASE_URL,
            token=settings.LETTA_TOKEN
        )
        self.logger = logging.getLogger("agent_service")
        
        # Data storage
        self.session_data = {}
        self.sent_session_ids = set()
        self.loaded_templates = {}
        self.agent_categories = {}
        self.shared_memory_blocks = {}  # Cache for loaded shared memory blocks
        self.session_welcome_sent = {}  # Track welcome messages per session
        self.custom_tools = {}  # Store registered custom tools
        self.mcp_tools = {}  # Store registered MCP tools
        
        self.logger.info(f"Custom tools registered: {list(self.custom_tools.keys())}")
        self.logger.info(f"MCP tools registered: {list(self.mcp_tools.keys())}")

        # Session persistence file path
        self.sessions_file = os.path.join(settings.FOLDERS_BASE_DIR, "sessions.json")
        
        # Discover and register ALL custom tools dynamically
        self.logger.info("=== Starting Dynamic Custom Tool Registration ===")
        registration_result = self.register_custom_tools()
        self.logger.info(f"=== Custom Tool Registration Complete: {registration_result.get('total_registered', 0)} tools registered ===")

        # Register MCP tools dynamically
        self.logger.info(">>> [MCP] Starting MCP registration process...")
        self.register_mcp_tools()

        # Load existing sessions from file
        self._load_sessions_from_file()
        
        # Load templates on startup
        self._load_templates_from_folders()
        # Register all custom tools dynamically with improved handling
        # (This line is now redundant as registration happens above)
        
        # Load existing sessions from file
        self._load_sessions_from_file()
        
        # Load templates on startup
        self._load_templates_from_folders()
    
    def discover_custom_tool_structure(self):
        """Discover all custom tools in the directory structure"""
        possible_base_paths = [
            os.path.join(os.path.dirname(self.settings.TEMPLATES_DIR), "custom_tool"),
            "/app/data/custom_tool",
            "/root/my.continuia/raccha.ai/Raccha/data/custom_tool"
        ]
        
        discovered_tools = []
        found_base_path = None
        
        # Find which base path exists
        for base_path in possible_base_paths:
            if os.path.exists(base_path):
                found_base_path = base_path
                self.logger.info(f"Found custom_tool directory at: {base_path}")
                break
        
        if not found_base_path:
            self.logger.info("No custom_tool directory found at any expected location.")
            return {
                "success": False,
                "base_path": None,
                "tools": [],
                "searched_paths": possible_base_paths
            }

        try:
            # Scan for tool directories
            for item in os.listdir(found_base_path):
                tool_path = os.path.join(found_base_path, item)
                
                # Skip system directories and files
                if (item.startswith('.') or 
                    item == '__pycache__' or 
                    item.startswith('__') or
                    not os.path.isdir(tool_path)):
                    continue
                
                tool_info = {
                    "name": item,
                    "path": tool_path,
                    "has_register_py": os.path.exists(os.path.join(tool_path, "register.py")),
                    "python_files": [],
                    "other_files": []
                }
                
                # Scan files in tool directory
                try:
                    for file in os.listdir(tool_path):
                        if file.startswith('.') or file.startswith('__'):
                            continue
                            
                        file_path = os.path.join(tool_path, file)
                        if os.path.isfile(file_path):
                            if file.endswith('.py') and file != 'register.py':
                                tool_info["python_files"].append(file)
                            elif file != 'register.py':
                                tool_info["other_files"].append(file)
                    
                    discovered_tools.append(tool_info)
                    self.logger.info(f"Discovered tool: {item} (register.py: {tool_info['has_register_py']}, python files: {len(tool_info['python_files'])})")
                
                except Exception as scan_error:
                    self.logger.warning(f"Error scanning tool directory {tool_path}: {scan_error}")
            
            return {
                "success": True,
                "base_path": found_base_path,
                "tools": discovered_tools,
                "total_tools_found": len(discovered_tools)
            }
            
        except Exception as e:
            self.logger.error(f"Error discovering custom tools: {e}")
            return {
                "success": False,
                "base_path": found_base_path,
                "tools": [],
                "error": str(e)
            }

    def find_existing_tool_by_name(self, tool_name: str):
        """Find an existing tool by name using the list API"""
        try:
            # Use the list API to find tools by name
            tools = self.client.tools.list(name=tool_name)
            if tools and len(tools) > 0:
                # Return the first matching tool
                return tools[0]
            
            # If name filter didn't work, search through all tools
            all_tools = self.client.tools.list(limit=1000)  # Get more tools to search
            for tool in all_tools:
                if hasattr(tool, 'name') and tool.name == tool_name:
                    return tool
            return None
        except Exception as e:
            self.logger.error(f"Error finding tool {tool_name}: {e}")
            return None

    def register_tool_from_function(self, func, tool_name: str, description: str = None, tags: list = None):
        """Idempotently register/update a tool in Letta using tools.upsert."""

        if not description:
            description = f"Custom tool: {tool_name} for Continuia Health system"
        if not tags:
            tags = ["custom", "raccha.ai", "continuia", tool_name]

        try:
            # Extract function source code
            try:
                source_code = textwrap.dedent(inspect.getsource(func))
            except Exception:
                self.logger.warning(f"Could not extract source for {tool_name}, using fallback stub")
                source_code = f"def {tool_name}(*args, **kwargs):\n    return 'Function body unavailable'"

            # Upsert tool (create or update if already exists)
            tool = self.client.tools.upsert(
                source_code=source_code,
                description=description,
                tags=tags,
                source_type="python"
            )

            # Track registered tool
            final_tool_name = getattr(tool, "name", tool_name)
            self.custom_tools[final_tool_name] = tool.id
            self.logger.info(f"✅ Upserted tool: {final_tool_name} (ID: {tool.id})")

            return tool

        except Exception as e:
            self.logger.error(f"❌ Upsert failed for {tool_name}: {e}")
            return None
            
    def register_custom_tools(self):
        """Dynamically discover and register ALL custom tools found in the directory structure"""
        
        # First, cache all existing tools to avoid API calls
        self.logger.info("Caching existing tools from Letta...")
        try:
            all_existing_tools = self.client.tools.list(limit=1000)
            existing_tools_cache = {}
            for tool in all_existing_tools:
                if hasattr(tool, 'name') and tool.name:
                    existing_tools_cache[tool.name] = tool
            self.logger.info(f"Cached {len(existing_tools_cache)} existing tools")
        except Exception as e:
            self.logger.warning(f"Failed to cache existing tools: {e}")
            existing_tools_cache = {}
        
        # Check for our known tools first #within api send msg to agent(inside send msg to agent....first search adn send the msg)
        tools_already_registered = 0
        # Change this line in register_custom_tools() method:
        for tool_name in ["add_numbers", "send_otp", "verify_otp_code"]:  # ✅ Added both OTP tools
            # Add other expected tool names here
            if tool_name in existing_tools_cache:
                existing_tool = existing_tools_cache[tool_name]
                self.custom_tools[tool_name] = existing_tool.id
                self.logger.info(f"✅ Using existing cached tool: {tool_name} (ID: {existing_tool.id})")
                tools_already_registered += 1
        
        # Discover all available tools
        discovery_result = self.discover_custom_tool_structure()
        
        if not discovery_result["success"]:
            self.logger.info("No custom tools discovered, checking fallback registrations...")
            if tools_already_registered > 0:
                self.logger.info(f"Already found {tools_already_registered} existing tools")
                return {
                    "total_registered": tools_already_registered,
                    "registration_results": [],
                    "discovered_tools": 0,
                    "cached_existing": tools_already_registered
                }
            else:
                return self._attempt_fallback_tool_registrations()
        
        base_path = discovery_result["base_path"]
        discovered_tools = discovery_result["tools"]
        
        self.logger.info(f"Discovered {len(discovered_tools)} potential custom tools")
        
        tools_registered = tools_already_registered
        registration_results = []
        
        # Process each discovered tool
        for tool_info in discovered_tools:
            tool_name = tool_info["name"]
            tool_path = tool_info["path"]
            
            registration_result = {
                "tool_name": tool_name,
                "tool_path": tool_path,
                "method": None,
                "success": False,
                "error": None,
                "tool_id": None
            }
            
            # Check if we already found this tool in cache
            if tool_name in self.custom_tools:
                registration_result.update({
                    "method": "cached_existing",
                    "success": True,
                    "tool_id": self.custom_tools[tool_name],
                    "message": "Tool already exists, using cached version"
                })
                registration_results.append(registration_result)
                self.logger.info(f"✅ Skipping {tool_name} - already cached")
                continue
            
            # Method 1: Try file-based registration (register.py)
            if tool_info["has_register_py"]:
                reg_path = os.path.join(tool_path, "register.py")
                try:
                    self.logger.info(f"Attempting file-based registration for {tool_name} from {reg_path}")
                    
                    import importlib.util
                    import sys
                    
                    # Create unique module name to avoid conflicts
                    module_name = f"register_module_{tool_name}_{hash(reg_path) % 10000}"
                    
                    spec = importlib.util.spec_from_file_location(module_name, reg_path)
                    module = importlib.util.module_from_spec(spec)
                    
                    # Add to sys.modules temporarily
                    sys.modules[module_name] = module
                    spec.loader.exec_module(module)

                    if hasattr(module, "register"):
                        tool_obj = module.register(self.client, self.logger)
                        if tool_obj:
                            final_tool_name = getattr(tool_obj, 'name', tool_name)
                            self.custom_tools[final_tool_name] = tool_obj.id
                            registration_result.update({
                                "method": "file_based_register_py",
                                "success": True,
                                "tool_id": tool_obj.id,
                                "final_name": final_tool_name
                            })
                            tools_registered += 1
                            self.logger.info(f"✅ File-based registration successful: {final_tool_name}")
                        else:
                            registration_result["error"] = "register() returned None"
                            self.logger.warning(f"⚠️ register() returned None for {tool_name}")
                    else:
                        registration_result["error"] = "No register() function found"
                        self.logger.warning(f"⚠️ No register() function in {reg_path}")
                    
                    # Clean up module
                    if module_name in sys.modules:
                        del sys.modules[module_name]

                except Exception as e:
                    registration_result["error"] = str(e)
                    self.logger.error(f"❌ File-based registration failed for {tool_name}: {e}")
            
            # Method 2: Try auto-discovery from Python files (if file-based registration failed)
            if not registration_result["success"] and tool_info["python_files"]:
                try:
                    self.logger.info(f"Attempting auto-discovery for {tool_name} from Python files: {tool_info['python_files']}")
                    
                    # Try to auto-discover functions from Python files
                    for py_file in tool_info["python_files"]:
                        py_file_path = os.path.join(tool_path, py_file)
                        
                        try:
                            discovered_functions = self._discover_functions_from_file(py_file_path)
                            
                            for func_name, func_obj in discovered_functions.items():
                                if func_obj:
                                    tool_registered = self.register_tool_from_function(
                                        func=func_obj,
                                        tool_name=func_name,
                                        description=f"Auto-discovered tool: {func_name} from {tool_name}",
                                        tags=["custom", "raccha.ai", "continuia", tool_name, func_name, "auto_discovered"]
                                    )
                                    
                                    if tool_registered:
                                        registration_result.update({
                                            "method": f"auto_discovery_from_{py_file}",
                                            "success": True,
                                            "tool_id": tool_registered.id,
                                            "function_name": func_name,
                                            "source_file": py_file
                                        })
                                        tools_registered += 1
                                        self.logger.info(f"✅ Auto-discovery successful: {func_name} from {py_file}")
                                        break  # Register only the first valid function per file
                            
                            if registration_result["success"]:
                                break  # Stop trying other files if we succeeded
                                
                        except Exception as file_error:
                            self.logger.warning(f"⚠️ Failed to process {py_file}: {file_error}")
                            continue
                    
                    if not registration_result["success"]:
                        registration_result["error"] = f"No valid functions found in Python files: {tool_info['python_files']}"
                        
                except Exception as auto_error:
                    registration_result["error"] = f"Auto-discovery failed: {auto_error}"
                    self.logger.error(f"❌ Auto-discovery failed for {tool_name}: {auto_error}")
            
            # Skip placeholder creation to avoid unnecessary tools
            if not registration_result["success"]:
                self.logger.info(f"No valid registration method found for {tool_name}")
                registration_result["error"] = "No valid registration method found"
            
            registration_results.append(registration_result)
        
        self.logger.info(f"Total custom tools available: {len(self.custom_tools)} (cached: {tools_already_registered}, newly registered: {tools_registered - tools_already_registered})")
        self.logger.info(f"Custom tools: {list(self.custom_tools.keys())}")
        
        return {
            "total_registered": len(self.custom_tools),
            "registration_results": registration_results,
            "discovered_tools": len(discovered_tools),
            "cached_existing": tools_already_registered,
            "newly_registered": tools_registered - tools_already_registered
        }

    def _discover_functions_from_file(self, file_path: str) -> Dict[str, callable]:
        """Discover callable functions from a Python file"""
        discovered_functions = {}
        
        try:
            import importlib.util
            import inspect
            import sys
            
            # Create unique module name
            module_name = f"autodiscovery_{hash(file_path) % 10000}"
            
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if not spec or not spec.loader:
                self.logger.warning(f"Could not create spec for {file_path}")
                return discovered_functions
                
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            
            try:
                spec.loader.exec_module(module)
                
                # Get all functions from the module
                for name, obj in inspect.getmembers(module, inspect.isfunction):
                    # Skip private functions and imports
                    if (not name.startswith('_') and 
                        obj.__module__ == module_name and  # Only functions defined in this module
                        hasattr(obj, '__doc__')):  # Has documentation
                        
                        self.logger.info(f"Discovered function: {name} in {file_path}")
                        discovered_functions[name] = obj
                        
            except Exception as exec_error:
                self.logger.error(f"Failed to execute module {file_path}: {exec_error}")
                
            finally:
                # Clean up
                if module_name in sys.modules:
                    del sys.modules[module_name]
                    
        except Exception as e:
            self.logger.error(f"Failed to discover functions from {file_path}: {e}")
        
        return discovered_functions
    
    def _attempt_fallback_tool_registrations(self) -> dict:
        """Attempt to register common/fallback tools when no tools are discovered"""
        fallback_tools_registered = 0
        registration_results = []
        
        # ===== ADD_NUMBERS TOOL REGISTRATION =====
        existing_add_numbers = self.find_existing_tool_by_name("add_numbers")
        if existing_add_numbers:
            self.custom_tools["add_numbers"] = existing_add_numbers.id
            self.logger.info(f"✅ Found existing add_numbers tool, using it (ID: {existing_add_numbers.id})")
            fallback_tools_registered += 1
            registration_results.append({
                "tool_name": "add_numbers",
                "method": "existing_found",
                "success": True,
                "tool_id": existing_add_numbers.id
            })
        else:
            # Create add_numbers if it doesn't exist
            try:
                def add_numbers(a: float, b: float) -> str:
                    """Add two numbers together and return the result."""
                    result = a + b
                    return f"The sum of {a} and {b} is {result}. Calculation completed."
                
                add_numbers.__name__ = "add_numbers"
                add_numbers.__doc__ = "Add two numbers together and return the result."
                
                registered_tool = self.register_tool_from_function(
                    func=add_numbers,
                    tool_name="add_numbers",
                    description="Add two numbers together - fallback implementation",
                    tags=["math", "calculator", "addition", "fallback", "raccha.ai", "continuia"]
                )
                
                if registered_tool:
                    fallback_tools_registered += 1
                    self.logger.info(f"✅ Registered fallback add_numbers tool")
                    registration_results.append({
                        "tool_name": "add_numbers",
                        "method": "fallback_created",
                        "success": True,
                        "tool_id": registered_tool.id
                    })
                else:
                    registration_results.append({
                        "tool_name": "add_numbers",
                        "method": "fallback_creation_failed",
                        "success": False,
                        "error": "Registration returned None"
                    })
                        
            except Exception as e:
                self.logger.warning(f"Error registering fallback add_numbers tool: {e}")
                registration_results.append({
                    "tool_name": "add_numbers",
                    "method": "fallback_creation_failed",
                    "success": False,
                    "error": str(e)
                })
        
        self.logger.info(f"Available custom tools: {list(self.custom_tools.keys())}")
        
        return {
            "total_registered": fallback_tools_registered,
            "registration_results": registration_results
        }

    def test_custom_tool_with_agent(self, session_id: str, tool_name: str = "add_numbers", test_message: str = None):
        """Test a custom tool with a specific agent"""
        try:
            if session_id not in self.session_data:
                return {"error": "Session not found", "success": False}
            
            session = self.session_data[session_id]
            agent_id = session["agent_id"]
            
            if not test_message:
                test_message = f"Please use the {tool_name} tool to add 5 and 3 for me."
            
            # Send message to agent
            response = self.client.agents.messages.create(
                agent_id=agent_id,
                messages=[{
                    "role": "user",
                    "content": [{"type": "text", "text": test_message}]
                }]
            )
            
            # Extract response
            agent_response = self._extract_agent_response_from_letta(response)
            
            return {
                "success": True,
                "session_id": session_id,
                "agent_id": agent_id,
                "test_message": test_message,
                "agent_response": agent_response,
                "tool_used": tool_name
            }
            
        except Exception as e:
            self.logger.error(f"Error testing custom tool: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def register_mcp_tools(self):
        """Discover and register MCP tools from /data/mcp/*/register.py"""
        base_path = os.path.join(os.path.dirname(self.settings.TEMPLATES_DIR), "mcp")

        if not os.path.exists(base_path):
            self.logger.info("No MCP directory found.")
            return

        tools_registered = 0
        for root, dirs, files in os.walk(base_path):
            if "register.py" in files:
                reg_path = os.path.join(root, "register.py")
                try:
                    import importlib.util
                    spec = importlib.util.spec_from_file_location("mcp_register_module", reg_path)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    if hasattr(module, "register"):
                        tool_obj = module.register(self.client, self.logger)
                        if tool_obj:
                            tool_name = getattr(tool_obj, "name", os.path.basename(root))
                            self.mcp_tools[tool_name] = tool_obj.id
                            self.logger.info(f"✅ Registered MCP tool {tool_name} (ID: {tool_obj.id})")
                            tools_registered += 1
                        else:
                            self.logger.warning(f"⚠️ register() returned None in {reg_path}")
                    else:
                        self.logger.warning(f"⚠️ No register() function in {reg_path}")

                except Exception as e:
                    self.logger.error(f"❌ Failed to register MCP tool from {reg_path}: {e}")

        self.logger.info(f"Total MCP tools registered: {tools_registered} — {list(self.mcp_tools.keys())}")

    
    def _create_session_folders(self, session_id: str) -> Dict[str, str]:
        """Create and return folder paths for session-specific and continuia folders with robust permission handling"""
        base_folder_path = self.settings.FOLDERS_BASE_DIR
        
        # Create session-specific folder for user uploads
        session_folder_path = os.path.join(base_folder_path, f"session_{session_id}")
        
        # Continuia folder path (pre-existing or created)
        continuia_folder_path = os.path.join(base_folder_path, "continuia")
        
        def _create_folder_with_fallback(target_path: str, folder_type: str, session_id: str) -> str:
            """Helper function to create folder with permission fallbacks"""
            try:
                # First attempt: create with proper permissions
                os.makedirs(target_path, exist_ok=True, mode=0o755)
                
                # Test write permission
                test_file = os.path.join(target_path, f"test_write_{uuid.uuid4().hex[:8]}")
                try:
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)
                    self.logger.info(f"Created and verified {folder_type} folder: {target_path}")
                    return target_path
                except PermissionError:
                    self.logger.warning(f"Write test failed for {folder_type} folder: {target_path}")
                    raise PermissionError("Write test failed")
                    
            except (PermissionError, OSError) as e:
                self.logger.warning(f"Permission denied creating {folder_type} folder: {e}")
                
                # Fallback 1: Try with different permissions
                try:
                    os.makedirs(target_path, exist_ok=True, mode=0o777)
                    # Test write again
                    test_file = os.path.join(target_path, f"test_write_{uuid.uuid4().hex[:8]}")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)
                    self.logger.info(f"Created {folder_type} folder with 777 permissions: {target_path}")
                    return target_path
                except Exception:
                    pass
                
                # Fallback 2: Try /tmp location
                if folder_type == "session":
                    fallback_path = os.path.join("/tmp", f"session_{session_id}")
                else:
                    fallback_path = "/tmp/continuia"
                
                try:
                    os.makedirs(fallback_path, exist_ok=True)
                    # Test write permission
                    test_file = os.path.join(fallback_path, f"test_write_{uuid.uuid4().hex[:8]}")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)
                    self.logger.info(f"Created fallback {folder_type} folder: {fallback_path}")
                    return fallback_path
                except Exception as fallback_error:
                    self.logger.error(f"Fallback folder creation failed: {fallback_error}")
                
                # Fallback 3: Use current working directory
                cwd_fallback = os.path.join(os.getcwd(), f"{folder_type}_{session_id}")
                try:
                    os.makedirs(cwd_fallback, exist_ok=True)
                    test_file = os.path.join(cwd_fallback, f"test_write_{uuid.uuid4().hex[:8]}")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)
                    self.logger.info(f"Created CWD fallback {folder_type} folder: {cwd_fallback}")
                    return cwd_fallback
                except Exception as cwd_error:
                    self.logger.error(f"CWD fallback failed: {cwd_error}")
                    raise Exception(f"Unable to create writable {folder_type} folder anywhere")
        
        try:
            # Create session folder with fallbacks
            final_session_path = _create_folder_with_fallback(
                session_folder_path, "session", session_id
            )
            
            # Create continuia folder with fallbacks
            final_continuia_path = _create_folder_with_fallback(
                continuia_folder_path, "continuia", session_id
            )
            
            return {
                "session_folder": final_session_path,
                "continuia_folder": final_continuia_path
            }
            
        except Exception as e:
            self.logger.error(f"Critical error: Failed to create any folders for session {session_id}: {e}")
            # Return read-only paths as last resort
            return {
                "session_folder": "/tmp",
                "continuia_folder": "/tmp"
            }
    
    def _attach_folders_to_agent(self, agent_id: str, folder_paths: Dict[str, str]) -> list:
        """Create and attach folders to the agent using correct Letta API"""
        try:
            attached_folders = []
            
            # Create and attach session folder
            session_folder = folder_paths.get("session_folder")
            if session_folder and os.path.exists(session_folder):
                try:
                    # Create folder in Letta system
                    session_folder_name = f"session_uploads_{agent_id[-8:]}"
                    session_folder_result = self.client.folders.create(
                        name=session_folder_name,
                        description=f"Session uploads folder for agent {agent_id}",
                        instructions="""
# Session Uploads Folder - User Medical Documents
_Contains files uploaded by users during their healthcare intake session_

## Document Types Expected
- **Medical Records** — Hospital records, clinic notes, discharge summaries
- **Lab Results** — Blood tests, urine tests, imaging reports (X-ray, CT, MRI)
- **Prescription Information** — Current medications, prescription bottles, pharmacy records
- **Insurance Documents** — Insurance cards, coverage information, prior authorizations
- **Medical Images** — Photos of symptoms, skin conditions, injuries, medical devices
- **Referral Letters** — Doctor referrals, specialist recommendations
- **Previous Test Results** — Cardiac tests (EKG, stress tests), pulmonary function tests
- **Vaccination Records** — Immunization history, COVID vaccination cards
- **Personal Health Records** — Blood pressure logs, glucose readings, symptom diaries

## Document Processing Guidelines

### When Processing Uploaded Files:
- **Always acknowledge** receipt and review of uploaded documents
- **Extract key medical information** including dates, test values, diagnoses, medications
- **Summarize findings** in clear, patient-friendly language
- **Note any red flags** or concerning values that require immediate attention
- **Cross-reference** information between multiple uploaded documents
- **Ask for clarification** if documents are unclear, incomplete, or contradictory
- **Respect patient privacy** - treat all information as confidential

### Response Format for Document Review:
- Start with: "I've reviewed your uploaded [document type]..."
- Provide key findings in bullet points
- Note dates and sources for all referenced information
- Include relevant measurements/values with normal ranges when applicable
- Flag any concerning findings for professional review

### Medical Document Analysis:
- **Lab Results**: Extract values, compare to normal ranges, note trends
- **Imaging Reports**: Summarize findings, note radiologist conclusions
- **Medical Records**: Extract diagnoses, treatment plans, follow-up instructions
- **Medication Lists**: Check for interactions, dosages, administration instructions
- **Symptom Photos**: Acknowledge receipt but cannot provide diagnostic opinions

### Important Limitations:
- **Cannot diagnose** based on uploaded documents alone
- **Cannot replace professional medical evaluation**
- **Cannot recommend treatment changes** without physician consultation
- Always advise users to discuss findings with their healthcare provider
- Educational information only - not medical advice

### File Organization:
- Documents are automatically processed and indexed for quick retrieval
- Agent can reference specific documents during conversation
- Multiple related documents can be cross-referenced for comprehensive review
- File metadata (upload time, file type, size) is tracked for context

### Privacy and Security:
- All uploaded documents are treated as confidential medical information
- Files are processed securely and access is limited to the current session
- Users should be reminded that sensitive medical information is being shared
- Compliance with healthcare privacy standards is maintained

**Remember**: This is intake coordination - not clinical diagnosis. Always direct users to appropriate medical professionals for clinical interpretation of their medical documents.
""",
                        embedding="openai/text-embedding-3-small"
                    )
                    
                    attached_folders.append({
                        "name": "session_uploads",
                        "path": session_folder,
                        "id": session_folder_result.id,
                        "letta_name": session_folder_name,
                        "writable": os.access(session_folder, os.W_OK)
                    })
                    self.logger.info(f"Created Letta folder for session: {session_folder_name} (ID: {session_folder_result.id})")
                    
                    # Now attach the folder to the agent
                    try:
                        attach_result = self.client.agents.folders.attach(
                            agent_id=agent_id,
                            folder_id=session_folder_result.id
                        )
                        self.logger.info(f"Attached session folder to agent {agent_id}")
                        attached_folders[-1]["attached"] = True
                    except Exception as attach_error:
                        self.logger.error(f"Failed to attach session folder to agent: {attach_error}")
                        attached_folders[-1]["attached"] = False
                    
                except Exception as e:
                    self.logger.error(f"Failed to create session folder in Letta: {e}")
                    # Add folder info even if Letta creation failed
                    attached_folders.append({
                        "name": "session_uploads",
                        "path": session_folder,
                        "id": None,
                        "letta_name": None,
                        "writable": os.access(session_folder, os.W_OK),
                        "status": "letta_creation_failed"
                    })
            else:
                self.logger.warning(f"Session folder does not exist: {session_folder}")
            
            # Create and attach continuia folder
            continuia_folder = folder_paths.get("continuia_folder")
            if continuia_folder and os.path.exists(continuia_folder):
                try:
                    # Create folder in Letta system
                    continuia_folder_name = f"continuia_resources_{agent_id[-8:]}"
                    continuia_folder_result = self.client.folders.create(
                        name=continuia_folder_name,
                        description="Continuia shared resources folder",
                        instructions="""
# Continuia Resources Folder - Official Documents
_Contains official Continuia platform documentation_

## Documents Available
- **Continuia Platform Handbook** — Official reference for licensed healthcare professionals
- **Marketing Interview Script** — Comprehensive marketing & sales resource
- **Additional company resources and documentation**

## Document Usage Guidelines

### Use the Platform Handbook when:
- Questions about **clinical operations, workflows, or medical processes**
- Healthcare professionals ask about **participation requirements**
- Inquiries about **compliance, data privacy, or regulatory** matters
- Questions about **compensation, ethics, or professional responsibilities**
- Technical questions about **platform features** for medical contributors
- Discussions about **quality assurance, governance, or clinical standards**
- Any query related to the **medical opinion delivery process**

### Use the Marketing Interview Script when:
- Prospective customers or partners ask about **company overview**
- Questions about **competitive positioning** or **market differentiation**
- Inquiries about **target markets, customer segments, or use cases**
- Discussions about **technology capabilities** and **AI features**
- Questions about **results, metrics, or success stories**
- **Business development** conversations or **partnership** discussions
- **Media interviews** or **public-facing communications**
- **Investor relations** or **strategic planning** discussions

## Response Guidelines

### Always Include
- Accurate information **directly from the relevant document**
- Appropriate **disclaimers** when discussing medical matters

- **Contact information:**
  - Clinical: **<EMAIL>**
  - Compliance: **<EMAIL>**
  - General Support & Onboarding: **<EMAIL>**
- Clear distinction between **educational information** and **medical advice**

### Never
- Provide **medical diagnoses** or **treatment recommendations**
- Share information that **contradicts official documents**
- **Invent** details not contained in source materials
- Discuss **confidential or proprietary** information beyond what's provided

## Escalation Protocol
Escalate when questions:
- Require **information not in these documents**
- Involve **complex regulatory or legal** matters
- Need **real-time pricing** or **availability** information
- Require **executive-level decision making**

**Direct users to appropriate contacts:**
- Clinical questions: **<EMAIL>**
- Compliance/regulatory: **<EMAIL>**
- General support: **<EMAIL>**

## Quality Standards
- ✅ Verify information **against source documents**
- ✅ Ensure **appropriate tone** for the audience
- ✅ Include **necessary disclaimers**
- ✅ Provide **clear next steps** or **contact information**
- ✅ Confirm response **does not exceed scope** of available information

Educational only. **Not medical advice.** No diagnoses or treatment recommendations.
""",
                        embedding="openai/text-embedding-3-small"
                    )
                    
                    attached_folders.append({
                        "name": "continuia_resources", 
                        "path": continuia_folder,
                        "id": continuia_folder_result.id,
                        "letta_name": continuia_folder_name,
                        "writable": os.access(continuia_folder, os.W_OK)
                    })
                    self.logger.info(f"Created Letta folder for continuia: {continuia_folder_name} (ID: {continuia_folder_result.id})")
                    
                    # Upload existing files from continuia folder to Letta
                    uploaded_files = self._upload_existing_files_to_letta(continuia_folder, continuia_folder_result.id)
                    attached_folders[-1]["uploaded_files"] = uploaded_files
                    
                    # Now attach the folder to the agent
                    try:
                        attach_result = self.client.agents.folders.attach(
                            agent_id=agent_id,
                            folder_id=continuia_folder_result.id
                        )
                        self.logger.info(f"Attached continuia folder to agent {agent_id}")
                        attached_folders[-1]["attached"] = True
                    except Exception as attach_error:
                        self.logger.error(f"Failed to attach continuia folder to agent: {attach_error}")
                        attached_folders[-1]["attached"] = False
                    
                except Exception as e:
                    self.logger.error(f"Failed to create continuia folder in Letta: {e}")
                    # Add folder info even if Letta creation failed
                    attached_folders.append({
                        "name": "continuia_resources",
                        "path": continuia_folder,
                        "id": None,
                        "letta_name": None,
                        "writable": os.access(continuia_folder, os.R_OK),
                        "status": "letta_creation_failed"
                    })
            else:
                self.logger.warning(f"Continuia folder does not exist: {continuia_folder}")
            
            return attached_folders
            
        except Exception as e:
            self.logger.error(f"Failed to attach folders to agent {agent_id}: {e}")
            return []
    
    def _upload_existing_files_to_letta(self, local_folder_path: str, letta_folder_id: str):
        """Upload existing files from local folder to Letta folder"""
        uploaded_files = []
        try:
            if not os.path.exists(local_folder_path):
                self.logger.info(f"Local folder does not exist: {local_folder_path}")
                return uploaded_files
            
            files_in_folder = [f for f in os.listdir(local_folder_path) if os.path.isfile(os.path.join(local_folder_path, f))]
            
            if not files_in_folder:
                self.logger.info(f"No files found in local folder: {local_folder_path}")
                return uploaded_files
            
            self.logger.info(f"Found {len(files_in_folder)} files to upload to Letta folder {letta_folder_id}")
            
            for filename in files_in_folder:
                file_path = os.path.join(local_folder_path, filename)
                try:
                    with open(file_path, 'rb') as f:
                        upload_result = self.client.folders.files.upload(
                            folder_id=letta_folder_id,
                            file=f
                        )
                        uploaded_files.append({
                            "filename": filename,
                            "letta_file_name": upload_result.file_name,
                            "status": upload_result.processing_status,
                            "file_size": upload_result.file_size
                        })
                        self.logger.info(f"Uploaded file to Letta: {filename} -> {upload_result.file_name} (Status: {upload_result.processing_status})")
                        
                except Exception as upload_error:
                    self.logger.error(f"Failed to upload file {filename} to Letta: {upload_error}")
                    uploaded_files.append({
                        "filename": filename,
                        "status": "upload_failed",
                        "error": str(upload_error)
                    })
            
            return uploaded_files
                    
        except Exception as e:
            self.logger.error(f"Error uploading existing files to Letta: {e}")
            return uploaded_files
    
    def _save_sessions_to_file(self):
        """Save all sessions to JSON file"""
        try:
            # Create a serializable copy of session data
            serializable_data = {}
            for session_id, data in self.session_data.items():
                serializable_data[session_id] = {
                    "agent_id": data["agent_id"],
                    "agent_name": data["agent_name"],
                    "template_name": data["template_name"],
                    "category": data["category"],
                    "agent_type": data["agent_type"],
                    "phase": data["phase"],
                    "case_id": data.get("case_id"),
                    "user_id": data.get("user_id"),
                    "folders": data.get("folders", {}),
                    "attached_folders": data.get("attached_folders", []),
                    "created_at": data.get("created_at", datetime.utcnow().isoformat()),
                    "last_accessed": datetime.utcnow().isoformat(),
                    "welcome_sent": data.get("welcome_sent", False)  # Track welcome message status
                }
            
            with open(self.sessions_file, 'w') as f:
                json.dump(serializable_data, f, indent=2, default=str)
            
            self.logger.info(f"Saved {len(serializable_data)} sessions to {self.sessions_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save sessions to file: {e}")
    
    def _load_sessions_from_file(self):
        """Load sessions from JSON file on startup"""
        try:
            if os.path.exists(self.sessions_file):
                with open(self.sessions_file, 'r') as f:
                    saved_sessions = json.load(f)
                
                # Load session data and welcome status
                self.session_data = saved_sessions
                for session_id, data in saved_sessions.items():
                    # Track which sessions already have welcome messages sent
                    if data.get("welcome_sent", False):
                        self.session_welcome_sent[session_id] = True
                
                self.logger.info(f"Loaded {len(saved_sessions)} sessions from {self.sessions_file}")
                self.logger.info(f"Welcome messages already sent for {len(self.session_welcome_sent)} sessions")
                
                # Log session details
                for session_id, data in saved_sessions.items():
                    self.logger.info(f"  - Session {session_id}: {data['agent_type']} ({data['phase']}) - Welcome: {data.get('welcome_sent', False)}")
            else:
                self.logger.info(f"No existing sessions file found at {self.sessions_file}")
                self.session_data = {}
                
        except Exception as e:
            self.logger.error(f"Failed to load sessions from file: {e}")
            self.session_data = {}
    
    def _load_shared_memory_blocks(self, base_dir: str):
        """Load shared memory blocks from the shared_memory directory"""
        shared_memory_path = os.path.join(base_dir, "shared_memory")
        shared_blocks = {}
        
        if not os.path.exists(shared_memory_path):
            self.logger.info(f"No shared_memory folder found at: {shared_memory_path}")
            return shared_blocks
        
        self.logger.info(f"Loading shared memory blocks from: {shared_memory_path}")
        
        try:
            for filename in os.listdir(shared_memory_path):
                if filename.endswith(('.yaml', '.yml')):
                    memory_file_path = os.path.join(shared_memory_path, filename)
                    memory_name = os.path.splitext(filename)[0]
                    
                    try:
                        with open(memory_file_path, 'r') as file:
                            memory_data = yaml.safe_load(file)
                            if memory_data and 'memory_block' in memory_data:
                                shared_blocks[memory_name] = memory_data['memory_block']
                                self.logger.info(f"Loaded shared memory: {memory_name}")
                    except Exception as e:
                        self.logger.error(f"Failed to load shared memory {filename}: {e}")
        except Exception as e:
            self.logger.error(f"Error scanning shared memory folder: {e}")
        
        return shared_blocks
    
    def _load_templates_from_folders(self):
        """Load templates from folder structure with shared memory support"""
        templates_base_dir = self.settings.TEMPLATES_DIR
        self.logger.info(f"Loading templates from: {templates_base_dir}")
        
        try:
            if not os.path.exists(templates_base_dir):
                self.logger.error(f"Templates base directory not found: {templates_base_dir}")
                return
            
            # Load shared memory blocks once from the shared location
            shared_memory_blocks = self._load_shared_memory_blocks(os.path.dirname(templates_base_dir))
            if shared_memory_blocks:
                self.shared_memory_blocks = shared_memory_blocks
                self.logger.info(f"Loaded {len(shared_memory_blocks)} shared memory blocks")
            
            # Scan through category folders
            for category_name in os.listdir(templates_base_dir):
                category_path = os.path.join(templates_base_dir, category_name)
                
                # Skip if not a directory
                if not os.path.isdir(category_path):
                    continue
                
                self.logger.info(f"Scanning category: {category_name}")
                category_templates = []
                
                # Load YAML template files in category folder
                for filename in os.listdir(category_path):
                    if filename.endswith(('.yaml', '.yml')):
                        template_path = os.path.join(category_path, filename)
                        template_name = os.path.splitext(filename)[0]
                        
                        try:
                            with open(template_path, 'r') as file:
                                template_data = yaml.safe_load(file)
                                if template_data and 'agent_template' in template_data:
                                    # Store with category prefix for uniqueness
                                    full_template_name = f"{category_name}_{template_name}"
                                    
                                    # Add shared memory blocks to template if they exist
                                    if shared_memory_blocks:
                                        template_data['shared_memory_blocks'] = shared_memory_blocks
                                    
                                    self.loaded_templates[full_template_name] = template_data
                                    
                                    # Add metadata about category
                                    if 'metadata' not in template_data:
                                        template_data['metadata'] = {}
                                    template_data['metadata']['category'] = category_name
                                    template_data['metadata']['original_name'] = template_name
                                    template_data['metadata']['has_shared_memory'] = bool(shared_memory_blocks)
                                    template_data['metadata']['shared_memory_count'] = len(shared_memory_blocks)
                                    
                                    category_templates.append(template_name)
                                    self.logger.info(f"Loaded template: {category_name}/{template_name}")
                                    if shared_memory_blocks:
                                        self.logger.info(f"  - Added {len(shared_memory_blocks)} shared memory blocks")
                        except Exception as e:
                            self.logger.error(f"Failed to load template {filename}: {e}")
                
                # Store category info
                if category_templates:
                    self.agent_categories[category_name] = {
                        "templates": category_templates,
                        "count": len(category_templates),
                        "default": category_templates[0],  # First template as default
                        "has_shared_memory": bool(shared_memory_blocks),
                        "shared_memory_blocks": list(shared_memory_blocks.keys()) if shared_memory_blocks else []
                    }
                        
        except Exception as e:
            self.logger.error(f"Template loading error: {e}")
        
        self.logger.info(f"Loaded {len(self.loaded_templates)} templates across {len(self.agent_categories)} categories")
        self.logger.info(f"Categories: {list(self.agent_categories.keys())}")
        self.logger.info(f"Shared memory blocks available: {list(self.shared_memory_blocks.keys())}")
    
    async def create_session(self, category: str = "health_care", agent_name: str = None):
        """Create new session with agent from category/agent combination and attach folders"""
        
        # Apply name mappings from settings if they exist
        original_category = category
        original_agent_name = agent_name
        
        # Map category name to template category if mapping exists
        if hasattr(self.settings, 'get_template_category_name'):
            category = self.settings.get_template_category_name(category)
        
        # Map agent name to template agent name if mapping exists and agent_name provided
        if agent_name and hasattr(self.settings, 'get_template_agent_name'):
            agent_name = self.settings.get_template_agent_name(agent_name)
        
        self.logger.info(f"Creating session - Original: {original_category}/{original_agent_name}")
        self.logger.info(f"Mapped to: {category}/{agent_name}")
        self.logger.info(f"Available categories: {list(self.agent_categories.keys())}")
        self.logger.info(f"Available templates: {list(self.loaded_templates.keys())}")
        
        # Determine template name
        if agent_name:
            template_name = f"{category}_{agent_name}"
        else:
            # Use default agent for category
            if category in self.agent_categories:
                default_agent = self.agent_categories[category]["default"]
                template_name = f"{category}_{default_agent}"
            else:
                # Use first available template if category not found
                if self.loaded_templates:
                    template_name = list(self.loaded_templates.keys())[0]
                    # Extract category from template name for consistency
                    extracted_category = template_name.split('_')[0]
                    category = extracted_category  # Update category to match template
                else:
                    raise ValueError("No templates available")
        
        self.logger.info(f"Determined template_name: {template_name}")
        
        if template_name not in self.loaded_templates:
            available = []
            for cat, info in self.agent_categories.items():
                for agent in info["templates"]:
                    available.append(f"{cat}/{agent}")
            
            # Provide helpful error message with original names for user clarity
            raise ValueError(
                f"Template '{original_category}/{original_agent_name or 'default'}' not found. "
                f"Mapped to '{category}/{agent_name or 'default'}' -> '{template_name}'. "
                f"Available: {available}"
            )
        
        session_id = uuid.uuid4().hex[:7]
        self.logger.info(f"Creating session: {session_id} with template: {template_name}")
        
        try:
            # Create session and continuia folders with robust permission handling
            folder_paths = self._create_session_folders(session_id)
            
            agent = await self._create_agent_from_template(
                template_name=template_name,
                session_id=session_id,
                phase="anonymous"
            )
            
            # Attach folders to the agent
            attached_folders = self._attach_folders_to_agent(agent.id, folder_paths)
            
            # Extract the actual category from template metadata to ensure consistency
            template_data = self.loaded_templates[template_name]
            actual_category = template_data['metadata']['category']
            actual_agent_type = template_data['metadata']['original_name']
            
            self.session_data[session_id] = {
                "agent_id": agent.id,
                "agent_name": agent.name,
                "template_name": template_name,
                "category": actual_category,  # Use actual category from template
                "agent_type": actual_agent_type,  # Use actual agent type from template
                "phase": "anonymous",
                "case_id": None,
                "user_id": None,
                "folders": {
                    "session_folder": folder_paths["session_folder"],
                    "continuia_folder": folder_paths["continuia_folder"]
                },
                "attached_folders": attached_folders,
                "created_at": datetime.utcnow().isoformat(),
                "welcome_sent": False,  # Track welcome message status for new session
                "original_request": {  # Store original request for debugging
                    "category": original_category,
                    "agent_name": original_agent_name
                }
            }
            
            # Save sessions to file
            self._save_sessions_to_file()
            
            # Send session ID to the agent as a user message immediately after creation
            try:
                self.logger.info(f"Sending session ID {session_id} to agent {agent.id}")
                session_message = f"Session ID: {session_id}"
                
                response = self.client.agents.messages.create(
                    agent_id=agent.id,
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": session_message}
                            ]
                        }
                    ]
                )
                
                self.logger.info(f"Session ID message sent to agent {agent.id}")
            except Exception as e:
                self.logger.error(f"Failed to send session ID to agent {agent.id}: {e}")
                # Don't fail the session creation if this fails
            
            self.logger.info(f"Created session {session_id} with agent {agent.name}")
            self.logger.info(f"Session data: category={actual_category}, agent_type={actual_agent_type}")
            self.logger.info(f"Attached {len(attached_folders)} folders to agent")
            self.logger.info(f"Welcome message status for new session {session_id}: False")
            
            return {
                "sessionId": session_id,
                "agentId": agent.id
            }
            
        except Exception as e:
            self.logger.error(f"Error in create_session: {str(e)}")
            # Clean up session data if it was partially created
            if session_id in self.session_data:
                del self.session_data[session_id]
            raise e

    async def authenticate_session(self, session_id: str):
        """Transition session to authenticated phase"""
        if session_id not in self.session_data:
            raise ValueError("Session not found")
        
        current_session = self.session_data[session_id]
        template_name = current_session["template_name"]
        
        # Generate IDs
        case_id = uuid.uuid4().hex[:12]
        user_id = uuid.uuid4().hex[:12]
        
        # Extract data from current agent
        scratch_data = self._extract_scratch_data(current_session["agent_id"])
        
        # Create new authenticated agent
        new_agent = await self._create_agent_from_template(
            template_name=template_name,
            session_id=session_id,
            phase="authenticated",
            case_id=case_id,
            user_id=user_id,
            scratch_data=scratch_data
        )
        
        # Re-attach folders to new agent
        folder_paths = current_session["folders"]
        attached_folders = self._attach_folders_to_agent(new_agent.id, folder_paths)
        
        # Update session (preserve welcome_sent status)
        welcome_sent_status = current_session.get("welcome_sent", False)
        self.session_data[session_id].update({
            "agent_id": new_agent.id,
            "agent_name": new_agent.name,
            "phase": "authenticated",
            "case_id": case_id,
            "user_id": user_id,
            "previous_agent_id": current_session["agent_id"],
            "attached_folders": attached_folders,  # Update with new attachments
            "authenticated_at": datetime.utcnow().isoformat(),
            "welcome_sent": welcome_sent_status  # Preserve welcome message status
        })
        
        # Save sessions to file
        self._save_sessions_to_file()
        
        self.logger.info(f"Session {session_id} authenticated - Case: {case_id}")
        self.logger.info(f"Re-attached {len(attached_folders)} folders to new agent")
        self.logger.info(f"Welcome message status preserved: {welcome_sent_status}")
        
        return {
            "sessionId": session_id,
            "agentId": new_agent.id,
            "caseId": case_id,
            "userId": user_id,
            "attachedFolders": attached_folders
        }

    def register_mcp_tools(self):
        """Register only the reddit_search tool from reddit_mcp (idempotent)."""
        self.logger.info(">>> [MCP] Starting MCP registration process...")
        tools_registered = 0
        try:
            # Step 1: Cache existing tools
            all_tools = self.client.tools.list(limit=1000)
            existing = {t.name: t for t in all_tools if hasattr(t, "name")}
            if "reddit_search" in existing:
                self.mcp_tools["reddit_search"] = existing["reddit_search"].id
                self.logger.info(f"✅ Using existing MCP tool reddit_search (ID: {existing['reddit_search'].id})")
                return

            # Step 2: Register if not found
            tool = self.client.tools.add_mcp_tool(
                mcp_server_name="reddit_mcp",
                mcp_tool_name="reddit_search"
            )
            self.mcp_tools["reddit_search"] = tool.id
            tools_registered += 1
            self.logger.info(f"✅ Registered new MCP tool reddit_search (ID: {tool.id})")

        except Exception as e:
            self.logger.error(f"❌ Failed to register reddit_search MCP tool: {e}")

        self.logger.info(f"Total MCP tools registered: {tools_registered} — {list(self.mcp_tools.keys())}")
        
    async def _create_agent_from_template(self, template_name: str, session_id: str, 
                                phase: str, case_id: str = None, user_id: str = None,
                                scratch_data: Dict = None):
        """Create agent from template with variable substitution and single scratch memory"""
        template = self.loaded_templates[template_name]
        agent_template = template['agent_template']
        
        # Generate instance number
        instance_no = uuid.uuid4().hex[:8]
        
        # Create agent name based on phase
        if phase == "anonymous":
            # For anonymous: full_template_name_session_id
            agent_name = f"{template_name}_{session_id}"
        elif phase == "authenticated" and case_id:
            # For authenticated: full_template_name_case_id  
            agent_name = f"{template_name}_{case_id}"
        else:
            agent_name = f"{template_name}_{instance_no}"
        
        # Build memory blocks with session_id populated - ONLY CREATE SCRATCH MEMORY
        memory_blocks = []
        
        # Find and process ONLY the scratch memory block
        for memory_block in agent_template.get('memory_blocks', []):
            block_value = memory_block['value']
            if session_id:
                block_value = block_value.replace("session_id: null", f"session_id: {session_id}")
            
            memory_blocks.append({
                "label": memory_block['label'],
                "value": block_value
            })
            self.logger.info(f"Created memory block: {memory_block['label']}")
        
        # If no scratch memory found in template, create a default one
        if not memory_blocks:
            default_scratch_value = f"""
    # Phase tracking
    current_phase: "{phase}"
    agent_name_status: "arika_reddy_{session_id}"

    # Session and conversation tracking
    session_id: {session_id}
    start_time: null
    conversation_turns: 0
    last_activity: null
    session_notes: []

    # User data collection
    user_symptoms: []
    user_complaints: []
    medical_details: {{}}
    personal_details:
    full_name: null
    phone: null
    email: null

    # File tracking
    uploaded_files: []
    file_insights: []

    # Workflow state
    collection_step: "collect_symptoms_and_name"
    ready_for_memory_transfer: false
    priority_intake: false
    red_flags_detected: []
    """
            memory_blocks.append({
                "label": "scratch",
                "value": default_scratch_value
            })
            self.logger.info(f"Created default scratch memory block for session {session_id}")
        
        # Get tools from template
        template_tools = agent_template.get('tools', [])
        custom_tools = agent_template.get('custom_tools', [])
        mcp_tools_list = agent_template.get('mcp_tools', [])  # NEW: Get MCP tools from template
        
        # Combine tool IDs for creation
        tool_ids = []
        
        # Add registered custom tool IDs
        for tool_name in custom_tools:
            if tool_name in self.custom_tools:
                tool_ids.append(self.custom_tools[tool_name])
                self.logger.info(f"Adding custom tool: {tool_name} (ID: {self.custom_tools[tool_name]})")
            else:
                self.logger.warning(f"Custom tool not registered: {tool_name}")
        
        for tool_name in mcp_tools_list:
            if tool_name.endswith("/*"):
                server_name = tool_name.split("/*")[0]
                if server_name in self.mcp_tools:
                    tool_ids.append(self.mcp_tools[server_name])
                    self.logger.info(f"Adding all tools from MCP server: {server_name}")
                else:
                    self.logger.warning(f"MCP server not registered: {server_name}")
            elif tool_name in self.mcp_tools:
                tool_ids.append(self.mcp_tools[tool_name])
                self.logger.info(f"Adding MCP tool: {tool_name} (ID: {self.mcp_tools[tool_name]})")
            else:
                self.logger.warning(f"MCP tool not registered: {tool_name}")

        create_params = {
            "name": agent_name,
            "system": agent_template['system_prompt'],
            "memory_blocks": memory_blocks,
            "tools": template_tools,
            "tags": [
                self.settings.POOL_TAG,
                f"template:{template_name}",
                f"instance:{instance_no}",
                f"phase:{phase}",
                f"session:{session_id}"
            ]
        }
        if tool_ids:
            create_params['tool_ids'] = tool_ids
        llm_config = agent_template.get('llm_config', {})
        if 'handle' in llm_config:
            create_params['model'] = llm_config['handle']
        else:
            create_params['model'] = "openai/gpt-4o-mini"
        embedding_config = agent_template.get('embedding_config', {})
        if 'embedding_model' in embedding_config:
            create_params['embedding'] = f"openai/{embedding_config['embedding_model']}"
        else:
            create_params['embedding'] = "openai/text-embedding-3-small"
        if 'identity_ids' in agent_template:
            create_params['identity_ids'] = agent_template['identity_ids']
        if case_id:
            create_params['tags'].append(f"case_id:{case_id}")
        if user_id:
            create_params['tags'].append(f"user_id:{user_id}")

        self.logger.info(f"Creating agent with {len(memory_blocks)} memory blocks")
        self.logger.info(f"Built-in tools: {template_tools}")
        self.logger.info(f"Custom tools: {custom_tools}")
        self.logger.info(f"MCP tools: {mcp_tools_list}")
        if tool_ids:
            self.logger.info(f"Tool IDs to attach: {tool_ids}")

        agent = self.client.agents.create(**create_params)
        self.logger.info(f"Created agent: {agent.name} (ID: {agent.id})")
        return agent

    def _extract_scratch_data(self, agent_id: str) -> Dict[str, Any]:
        """Extract scratch data from agent"""
        try:
            agent = self.client.agents.get(agent_id=agent_id)
            for block in agent.memory.blocks:
                if block.label == "scratch":
                    return {"extracted": "data", "content": block.value}
            return {}
        except Exception as e:
            self.logger.error(f"Failed to extract scratch data: {e}")
            return {}
    
    def _get_welcome_message(self, category: str, agent_type: str, template_name: str = None) -> str:
        """Get template-specific welcome message with fallback to category/agent-specific messages"""
        
        self.logger.info(f"Getting welcome message for template: {template_name}, category: {category}, agent_type: {agent_type}")
        
        # First try to get welcome message from template
        if template_name and template_name in self.loaded_templates:
            template_data = self.loaded_templates[template_name]
            template_welcome = template_data.get('agent_template', {}).get('welcome_message')
            if template_welcome:
                self.logger.info(f"Using template-specific welcome message from {template_name}")
                return template_welcome
        
        # Normalize category names to handle mismatches
        normalized_category = category.lower().replace('_', '').replace('-', '')
        
        # Updated welcome messages with both category naming conventions
        welcome_messages = {
            "healthcare": {
                "arika_reddy": "Hi! This is Arika Reddy from Continuia Healthcare. How can I assist you today?",
                "medical_intake": "Hello! I'm your medical intake specialist. Let's get started with your healthcare information.",
                "clinical_coordinator": "Welcome! I'm here to help coordinate your clinical care. What brings you here today?",
                "default": "Hello! I'm your healthcare assistant. How can I help you today?"
            },
            "health_care": {  # Support both naming conventions
                "arika_reddy": "Hi! This is Arika Reddy from Continuia Healthcare. How can I assist you today?",
                "medical_intake": "Hello! I'm your medical intake specialist. Let's get started with your healthcare information.", 
                "clinical_coordinator": "Welcome! I'm here to help coordinate your clinical care. What brings you here today?",
                "default": "Hello! I'm your healthcare assistant. How can I help you today?"
            },
            "legal": {
                "default": "Greetings! I'm your legal consultation assistant. What legal matter can I help you with?"
            },
            "finance": {
                "default": "Hello! I'm your financial planning assistant. How can I help with your financial needs?"
            }
        }
        
        # Try exact category match first
        category_messages = welcome_messages.get(category)
        if not category_messages:
            # Try normalized category match
            for key in welcome_messages.keys():
                if normalized_category == key.lower().replace('_', '').replace('-', ''):
                    category_messages = welcome_messages[key]
                    self.logger.info(f"Found category messages using normalized matching: {key} -> {category}")
                    break
        
        if not category_messages:
            self.logger.warning(f"No welcome messages found for category: {category}, using fallback")
            return f"Hello! I'm your {category} assistant. How can I help you today?"
        
        # Get agent-specific message or default for category
        agent_message = category_messages.get(agent_type)
        if agent_message:
            self.logger.info(f"Found agent-specific message for: {agent_type}")
            return agent_message
        
        # Try default for this category
        default_message = category_messages.get("default")
        if default_message:
            self.logger.info(f"Using default message for category: {category}")
            return default_message
        
        # Final fallback
        self.logger.warning(f"Using final fallback message")
        return "Hello! I'm here to assist you. How can I help you today?"
    
    def _extract_agent_response_from_letta(self, response) -> str:
        """Extract the actual agent message from Letta response, skipping reasoning content"""
        agent_response = "I'm here to help."
        
        try:
            self.logger.info(f"[ResponseExtract] Processing Letta response")
            
            # Method 1: Look for send_message tool calls first (this is the actual message)
            if hasattr(response, 'messages') and response.messages:
                for i, msg in enumerate(response.messages):
                    # Check for tool_calls with send_message
                    if hasattr(msg, 'tool_calls') and msg.tool_calls:
                        for tool_call in msg.tool_calls:
                            if (hasattr(tool_call, 'function') and 
                                hasattr(tool_call.function, 'name') and 
                                tool_call.function.name == 'send_message'):
                                try:
                                    import json
                                    args = json.loads(tool_call.function.arguments)
                                    if 'message' in args and args['message'].strip():
                                        agent_response = args['message'].strip()
                                        self.logger.info(f"[ResponseExtract] Found send_message: {agent_response[:50]}...")
                                        return agent_response
                                except Exception as e:
                                    self.logger.warning(f"[ResponseExtract] Tool call parse error: {e}")
            
            # Method 2: Check usage.steps_messages for send_message tool calls
            if (hasattr(response, 'usage') and 
                hasattr(response.usage, 'steps_messages') and 
                response.usage.steps_messages):
                
                for step_messages in response.usage.steps_messages:
                    if step_messages:
                        for step_msg in step_messages:
                            # Look for send_message tool calls in step messages
                            if hasattr(step_msg, 'tool_calls') and step_msg.tool_calls:
                                for tool_call in step_msg.tool_calls:
                                    if (hasattr(tool_call, 'function') and 
                                        hasattr(tool_call.function, 'name') and
                                        tool_call.function.name == 'send_message'):
                                        try:
                                            import json
                                            args = json.loads(tool_call.function.arguments)
                                            if 'message' in args and args['message'].strip():
                                                agent_response = args['message'].strip()
                                                self.logger.info(f"[ResponseExtract] Found step send_message: {agent_response[:50]}...")
                                                return agent_response
                                        except Exception as e:
                                            self.logger.warning(f"[ResponseExtract] Step tool parse error: {e}")
            
            # Method 3: Look for content that is NOT reasoning
            if hasattr(response, 'messages') and response.messages:
                for msg in response.messages:
                    if hasattr(msg, 'content'):
                        content = msg.content
                        
                        # Skip reasoning content
                        if isinstance(content, list):
                            for item in content:
                                if isinstance(item, dict):
                                    # Skip omitted_reasoning type
                                    if item.get('type') == 'omitted_reasoning':
                                        continue
                                    # Look for text type that's not reasoning
                                    elif item.get('type') == 'text' and 'text' in item:
                                        text = item['text']
                                        if (text and 
                                            not text.startswith('Session ID:') and
                                            not 'reasoning' in text.lower() and
                                            len(text.strip()) > 5):
                                            agent_response = text.strip()
                                            self.logger.info(f"[ResponseExtract] Found non-reasoning text: {agent_response[:50]}...")
                                            return agent_response
                        
                        elif isinstance(content, str):
                            # Skip if it looks like reasoning
                            if (content and 
                                not content.startswith('Session ID:') and
                                not 'reasoning' in content.lower() and
                                not content.startswith('User seems to') and
                                len(content.strip()) > 5):
                                agent_response = content.strip()
                                self.logger.info(f"[ResponseExtract] Found string content: {agent_response[:50]}...")
                                return agent_response
            
            # Method 4: Use regex to find send_message function calls in the full response
            response_str = str(response)
            
            # Look for send_message patterns
            import re
            send_message_patterns = [
                r'"name":\s*"send_message".*?"arguments":\s*"([^"]*\{[^}]*\}[^"]*)"',
                r'"name":\s*"send_message".*?"arguments":\s*"([^"]+)"',
                r'send_message.*?"message":\s*"([^"]+)"'
            ]
            
            for pattern in send_message_patterns:
                matches = re.findall(pattern, response_str, re.DOTALL)
                for match in matches:
                    try:
                        # Try to parse as JSON
                        if '{' in match and '}' in match:
                            import json
                            # Extract just the JSON part
                            json_start = match.find('{')
                            json_end = match.rfind('}') + 1
                            if json_start >= 0 and json_end > json_start:
                                json_part = match[json_start:json_end]
                                parsed = json.loads(json_part)
                                if 'message' in parsed and parsed['message']:
                                    agent_response = parsed['message'].strip()
                                    self.logger.info(f"[ResponseExtract] Found regex send_message: {agent_response[:50]}...")
                                    return agent_response
                    except Exception as e:
                        self.logger.warning(f"[ResponseExtract] Regex JSON parse error: {e}")
            
            # Method 5: Last resort - look for quoted messages that don't look like reasoning
            message_patterns = [
                r'"([^"]{20,}[.!?])"',  # Longer quoted strings with punctuation
            ]
            
            for pattern in message_patterns:
                matches = re.findall(pattern, response_str)
                for match in matches:
                    # Skip if it looks like reasoning, system messages, or technical content
                    skip_phrases = [
                        'session id:', 'user seems', 'reasoning', 'function', 'arguments',
                        'tool_call', 'omitted', 'debug', 'error', 'http', 'json'
                    ]
                    
                    if (match and 
                        len(match.strip()) > 15 and
                        not any(phrase in match.lower() for phrase in skip_phrases) and
                        # Check if it looks like a real message
                        any(word in match.lower() for word in ['you', 'i', 'can', 'please', 'help', 'what', 'how', 'thank', 'could'])):
                        
                        agent_response = match.strip()
                        self.logger.info(f"[ResponseExtract] Found message pattern: {agent_response[:50]}...")
                        return agent_response
            
        except Exception as e:
            self.logger.error(f"[ResponseExtract] Extraction error: {e}")
            import traceback
            self.logger.error(f"[ResponseExtract] Traceback: {traceback.format_exc()}")
        
        # If we still have default, log what we're getting
        if agent_response == "I'm here to help.":
            self.logger.error(f"[ResponseExtract] Failed to extract - logging first 1000 chars of response:")
            self.logger.error(f"{str(response)[:1000]}...")
        
        return agent_response
    
    def _is_session_id_message(self, content) -> bool:
        """Check if a message is the session ID message we want to skip"""
        if isinstance(content, str):
            return content.startswith('Session ID:')
        elif isinstance(content, list) and len(content) > 0:
            first_item = content[0]
            if isinstance(first_item, dict):
                text = first_item.get('text', '') or first_item.get('content', '')
                return text.startswith('Session ID:')
            elif isinstance(first_item, str):
                return first_item.startswith('Session ID:')
        return False
    
    async def get_agent_messages(self, session_id: str, batch: int = 200, include_welcome: bool = True):
        """Get messages from agent - returns clean conversation with welcome message first"""
        self.logger.info(f"[get_agent_messages] Called for session: {session_id}, include_welcome: {include_welcome}")
        
        try:
            if session_id not in self.session_data:
                self.logger.info(f"[get_agent_messages] Session {session_id} not found")
                return []
            
            session = self.session_data[session_id]
            agent_id = session["agent_id"]
            category = session["category"]
            agent_type = session["agent_type"]
            template_name = session.get("template_name")
            
            messages = []
            user_message_seen = False  # Track if we've seen the first user message
            
            # ALWAYS add welcome message as the first message if include_welcome is True
            if include_welcome:
                self.logger.info(f"[get_agent_messages] Adding welcome message as first message")
                welcome_content = self._get_welcome_message(category, agent_type, template_name)
                welcome_timestamp = session.get("created_at", datetime.utcnow().isoformat())
                
                welcome_msg = {
                    "id": f"welcome-{session_id}",
                    "role": "agent", 
                    "content": welcome_content,
                    "timestamp": welcome_timestamp
                }
                messages.append(welcome_msg)
                self.logger.info(f"[get_agent_messages] Welcome message added")
            
            # Get conversation messages using simplified approach
            try:
                self.logger.info(f"[get_agent_messages] Fetching conversation history from agent {agent_id}")
                
                # Simple approach - get all messages at once
                letta_messages = self.client.agents.messages.list(
                    agent_id=agent_id,
                    limit=1000,  # Large batch to get all messages
                    use_assistant_message=True,
                    assistant_message_tool_name="send_message",
                    assistant_message_tool_kwarg="message",
                    include_err=False
                )
                
                self.logger.info(f"[get_agent_messages] Retrieved {len(letta_messages)} messages from Letta")
                
                # DEBUG: Log all raw messages first
                for i, msg in enumerate(letta_messages):
                    msg_type = getattr(msg, 'message_type', 'unknown')
                    content_preview = ""
                    timestamp = getattr(msg, 'created_at', 'no_timestamp')
                    
                    # Try multiple ways to extract content
                    if hasattr(msg, 'content'):
                        if isinstance(msg.content, str):
                            content_preview = msg.content[:100]
                        elif isinstance(msg.content, list) and len(msg.content) > 0:
                            first_item = msg.content[0]
                            if isinstance(first_item, dict):
                                content_preview = first_item.get('text', first_item.get('content', str(first_item)[:100]))
                            else:
                                content_preview = str(first_item)[:100]
                    elif hasattr(msg, 'text'):
                        content_preview = msg.text[:100]
                    
                    # Show all attributes of the message for debugging
                    msg_attrs = [attr for attr in dir(msg) if not attr.startswith('_')]
                    
                    self.logger.info(f"[DEBUG] Raw message {i+1}: type={msg_type}, timestamp={timestamp}")
                    self.logger.info(f"[DEBUG] Content preview: {content_preview}")
                    self.logger.info(f"[DEBUG] Message attributes: {msg_attrs}")
                    
                    # Special check for tool calls or function calls
                    if hasattr(msg, 'tool_calls'):
                        self.logger.info(f"[DEBUG] Has tool_calls: {getattr(msg, 'tool_calls', None)}")
                    
                    self.logger.info(f"[DEBUG] ---")
                
                # Process and filter messages - KEEP BOTH USER AND AGENT MESSAGES
                processed_count = 0
                skipped_count = 0
                
                for i, msg in enumerate(letta_messages):
                    try:
                        self.logger.debug(f"[get_agent_messages] Processing message {i+1}/{len(letta_messages)}")
                        
                        if not hasattr(msg, 'message_type'):
                            self.logger.debug(f"[get_agent_messages] Message {i+1}: No message_type attribute")
                            skipped_count += 1
                            continue
                            
                        message_type = getattr(msg, 'message_type', '')
                        self.logger.debug(f"[get_agent_messages] Message {i+1}: type = {message_type}")
                        
                        # Process UserMessage and AssistantMessage types
                        if message_type in ['user_message', 'assistant_message']:
                            message_content = ""
                            message_role = "user" if message_type == "user_message" else "agent"
                            
                            # Extract content based on message structure
                            if hasattr(msg, 'content'):
                                if isinstance(msg.content, str):
                                    message_content = msg.content
                                elif isinstance(msg.content, list) and len(msg.content) > 0:
                                    for content_item in msg.content:
                                        if isinstance(content_item, dict):
                                            if content_item.get('type') == 'text' and content_item.get('text'):
                                                message_content = content_item['text']
                                                break
                                        elif isinstance(content_item, str):
                                            message_content = content_item
                                            break
                            elif hasattr(msg, 'text'):
                                message_content = msg.text
                            
                            self.logger.debug(f"[get_agent_messages] Message {i+1}: content = {message_content[:50]}...")
                            
                            # Skip empty messages
                            if not message_content or not message_content.strip():
                                self.logger.debug(f"[get_agent_messages] Message {i+1}: Empty content, skipping")
                                skipped_count += 1
                                continue
                            
                            # Track when we see the first user message
                            if message_role == "user":
                                user_message_seen = True
                                self.logger.debug(f"[get_agent_messages] Message {i+1}: First user message detected")
                            
                            # Skip agent messages that come before the first user message
                            # (These are usually automated responses triggered by session creation)
                            if message_role == "agent" and not user_message_seen:
                                self.logger.debug(f"[get_agent_messages] Message {i+1}: Skipping agent message before first user message: {message_content[:50]}...")
                                skipped_count += 1
                                continue
                            
                            # Apply existing message filtering
                            if self._should_skip_message(message_content, message_role):
                                self.logger.debug(f"[get_agent_messages] Message {i+1}: Filtered out: {message_content[:50]}...")
                                skipped_count += 1
                                continue
                            
                            # Clean up agent messages with malformed endings
                            if message_role == "agent":
                                original_content = message_content
                                message_content = self._clean_agent_message_content(message_content)
                                if original_content != message_content:
                                    self.logger.debug(f"[get_agent_messages] Message {i+1}: Cleaned agent content")
                                
                                # Skip if content became empty after cleaning
                                if not message_content.strip():
                                    self.logger.debug(f"[get_agent_messages] Message {i+1}: Empty after cleaning, skipping")
                                    skipped_count += 1
                                    continue
                            
                            # Create clean message object with timestamp
                            processed_msg = {
                                "id": getattr(msg, 'id', f"msg-{uuid.uuid4().hex[:8]}"),
                                "role": message_role,
                                "content": message_content.strip()
                            }

                            # Add timestamp if available
                            timestamp = None
                            for ts_field in ['created_at', 'timestamp', 'date', 'created']:
                                if hasattr(msg, ts_field):
                                    timestamp = getattr(msg, ts_field)
                                    break
                            
                            if not timestamp:
                                timestamp = datetime.utcnow().isoformat()
                            
                            processed_msg["timestamp"] = timestamp

                            messages.append(processed_msg)
                            processed_count += 1
                            
                            self.logger.debug(f"[get_agent_messages] Message {i+1}: Added {message_role} message")
                            
                        else:
                            self.logger.debug(f"[get_agent_messages] Message {i+1}: Skipping message type: {message_type}")
                            skipped_count += 1
                            
                    except Exception as msg_error:
                        self.logger.error(f"[get_agent_messages] Error processing message {i+1}: {msg_error}")
                        skipped_count += 1
                        continue

                self.logger.info(f"[get_agent_messages] Processing complete: {processed_count} messages added, {skipped_count} skipped")

            except Exception as e:
                self.logger.error(f"[get_agent_messages] Error fetching conversation history: {e}")
                import traceback
                self.logger.error(f"[get_agent_messages] Traceback: {traceback.format_exc()}")
            
            # Sort messages by timestamp, keeping welcome first
            welcome_msg = None
            conversation_messages = []
            
            for msg in messages:
                if msg.get('id', '').startswith('welcome-'):
                    welcome_msg = msg
                else:
                    # Apply content-based filtering even to already processed messages
                    content = msg.get('content', '').lower()
                    
                    # Skip messages with unwanted content patterns
                    unwanted_patterns = [
                        'thanks for reaching out',
                        'more human than human is our motto',
                        'hello! i\'m here to help you with your healthcare needs'
                    ]
                    
                    should_skip_by_content = any(pattern in content for pattern in unwanted_patterns)
                    
                    if should_skip_by_content:
                        self.logger.info(f"[get_agent_messages] Filtering out message by content: {msg.get('content', '')[:50]}...")
                        continue
                    
                    conversation_messages.append(msg)
            
            # Sort conversation messages by timestamp
            try:
                conversation_messages.sort(key=lambda x: x.get('timestamp', ''))
                self.logger.debug(f"[get_agent_messages] Sorted {len(conversation_messages)} conversation messages by timestamp")
            except Exception as sort_error:
                self.logger.warning(f"[get_agent_messages] Error sorting messages: {sort_error}")
            
            # Reconstruct messages with welcome first
            final_messages = []
            if welcome_msg:
                final_messages.append(welcome_msg)
            final_messages.extend(conversation_messages)
            
            self.logger.info(f"[get_agent_messages] Returning {len(final_messages)} messages total (welcome: {1 if welcome_msg else 0}, conversation: {len(conversation_messages)})")
            
            return final_messages
            
        except Exception as e:
            self.logger.error(f"[get_agent_messages] Exception: {e}")
            import traceback
            self.logger.error(f"[get_agent_messages] Traceback: {traceback.format_exc()}")
            return []

    def _should_skip_message(self, content: str, role: str) -> bool:
        """Determine if a message should be skipped based on content and role - simplified version"""
        if not content or not isinstance(content, str):
            return True
        
        content_lower = content.lower().strip()
        
        # Skip session ID messages explicitly
        if content.startswith('Session ID:'):
            return True
        
        # Skip JSON system messages (like login messages)
        if content.strip().startswith('{') and content.strip().endswith('}'):
            try:
                import json
                parsed = json.loads(content)
                if isinstance(parsed, dict) and 'type' in parsed:
                    system_types = ['login', 'heartbeat', 'system_alert', 'system_message']
                    if parsed['type'] in system_types:
                        return True
            except:
                pass
        
        # Skip automated system messages
        system_indicators = [
            'automated system message',
            'this is an automated',
            'hidden from the user',
            'continuing: continue tool rule',
            'prior messages have been hidden',
            'conversation memory constraints',
            'note: prior messages have been hidden'
        ]
        
        for indicator in system_indicators:
            if indicator in content_lower:
                return True
        
        return False


    def _clean_agent_message_content(self, content: str) -> str:
        """Clean up malformed agent message content"""
        if not content:
            return content
        
        # Remove malformed endings like ','request_heartbeat':false}','request_heartbeat':false}
        import re
        
        # Pattern to match malformed JSON endings
        patterns_to_remove = [
            r"',\s*'request_heartbeat':\s*false\s*\}.*$",
            r"',\s*'request_heartbeat':\s*true\s*\}.*$", 
            r"\}',\s*'request_heartbeat':\s*false\s*\}.*$",
            r"\}',\s*'request_heartbeat':\s*true\s*\}.*$",
            r"'\s*,\s*'request_heartbeat'.*$"
        ]
        
        cleaned_content = content
        for pattern in patterns_to_remove:
            cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.IGNORECASE)
        
        return cleaned_content.strip()
    
    async def handle_websocket(self, websocket: WebSocket, session_id: str, agent_name: str):
        """Handle WebSocket communication with OTP success detection and login data sending"""
        try:
            self.logger.info(f"[WebSocket] Starting handler - Session: {session_id}")
            
            if session_id not in self.session_data:
                self.logger.error(f"[WebSocket] Session {session_id} not found")
                try:
                    await websocket.send_json({
                        "type": "error",
                        "message": "Session not found. Please create a new session."
                    })
                except Exception as send_error:
                    self.logger.error(f"[WebSocket] Failed to send error message: {send_error}")
                return
            
            # Get all session information using session_id
            current_session = self.session_data[session_id]
            agent_id = current_session["agent_id"]
            template_name = current_session["template_name"]
            category = current_session["category"]
            agent_type = current_session["agent_type"]
            
            self.logger.info(f"[WebSocket] Session found - Category: {category}, Type: {agent_type}, Agent ID: {agent_id}")
            
            # Check welcome status and send welcome message if needed
            welcome_already_sent = current_session.get("welcome_sent", False)
            
            if not welcome_already_sent:
                self.logger.info(f"[WebSocket] Sending welcome message for session {session_id}")
                
                try:
                    welcome_content = self._get_welcome_message(category, agent_type, template_name)
                    self.logger.info(f"[WebSocket] Welcome content generated: {welcome_content[:50]}...")
                except Exception as e:
                    self.logger.error(f"[WebSocket] Error generating welcome: {e}")
                    welcome_content = "Hello! I'm here to assist you today."
                
                try:
                    await websocket.send_json({
                        "type": "connection_established",
                        "session_id": session_id,
                        "agent_id": agent_id,
                        "agent_name": agent_type,
                        "message": "Connected",
                        "content": welcome_content,
                        "UX_Command": False
                    })
                    
                    # Mark welcome as sent and save immediately
                    self.session_data[session_id]["welcome_sent"] = True
                    self.session_welcome_sent[session_id] = True
                    self._save_sessions_to_file()
                    
                    self.logger.info(f"[WebSocket] Welcome message sent and saved for session {session_id}")
                except Exception as welcome_send_error:
                    self.logger.error(f"[WebSocket] Failed to send welcome message: {welcome_send_error}")
                    return
            else:
                self.logger.info(f"[WebSocket] Welcome already sent for session {session_id}")
                try:
                    await websocket.send_json({
                        "type": "connection_established", 
                        "session_id": session_id,
                        "agent_id": agent_id,
                        "agent_name": agent_type,
                        "message": "Reconnected",
                        "content": None,
                        "UX_Command": False
                    })
                except Exception as reconnect_send_error:
                    self.logger.error(f"[WebSocket] Failed to send reconnection message: {reconnect_send_error}")
                    return
            
            # Message handling loop with WebSocket disconnection protection
            while True:
                try:
                    data = await websocket.receive_json()
                except Exception as receive_error:
                    self.logger.error(f"[WebSocket] Error receiving data: {receive_error}")
                    break
                
                user_message = self._extract_user_message(data)
                if not user_message:
                    try:
                        await websocket.send_json({
                            "type": "agent_response",
                            "session_id": session_id,
                            "agent_id": agent_id,
                            "agent_name": agent_type,
                            "content": "I didn't receive your message properly. Could you please try again?",
                            "message_type": "error",
                            "UX_Command": False
                        })
                    except Exception as error_send_error:
                        self.logger.error(f"[WebSocket] Failed to send error response: {error_send_error}")
                        break
                    continue
                
                # Send to agent with error handling
                try:
                    self.logger.info(f"[WebSocket] Sending user message to agent: {user_message[:50]}...")
                    
                    response = self.client.agents.messages.create(
                        agent_id=agent_id,
                        messages=[{
                            "role": "user",
                            "content": [{"type": "text", "text": user_message}]
                        }]
                    )
                    
                    # Extract the actual agent response
                    agent_response = self._extract_agent_response_from_letta(response)
                    
                    # Check if OTP was successfully verified
                    otp_success_detected = await self._check_otp_success_and_update_ux(agent_id, user_message, agent_response)
                    
                    if otp_success_detected:
                        self.logger.info(f"[WebSocket] OTP Success detected - extracting login data")
                        
                        login_data = await self._extract_login_data_from_agent(agent_id)
                        
                        if not login_data or login_data.get('phone') in ['verification_completed', 'extraction_failed', 'unknown']:
                            self.logger.warning(f"[WebSocket] Login data extraction failed or incomplete, using fallback")
                            login_data = {
                                'phone': 'extraction_failed',
                                'name': 'User',
                                'status': 'fallback_data'
                            }
                        
                        response_data = {
                            "type": "agent_response",
                            "session_id": session_id,
                            "agent_id": agent_id,
                            "agent_name": agent_type,
                            "content": agent_response,
                            "UX_Command": True,
                            "login": login_data,
                        }
                        
                        try:
                            await websocket.send_json(response_data)
                        except Exception as send_error:
                            self.logger.error(f"[WebSocket] Failed to send OTP success response: {send_error}")
                            break
                    else:
                        # Normal response without UX command
                        response_data = {
                            "type": "agent_response",
                            "session_id": session_id,
                            "agent_id": agent_id,
                            "agent_name": agent_type,
                            "content": agent_response,
                            "UX_Command": False
                        }
                        
                        try:
                            await websocket.send_json(response_data)
                        except Exception as send_error:
                            self.logger.error(f"[WebSocket] Failed to send normal response: {send_error}")
                            break
                    
                except Exception as e:
                    self.logger.error(f"[WebSocket] Agent communication error: {e}")
                    try:
                        await websocket.send_json({
                            "type": "agent_response",
                            "session_id": session_id,
                            "agent_id": agent_id,
                            "agent_name": agent_type,
                            "content": "I apologize for the technical issue. Could you please try again?",
                            "UX_Command": False
                        })
                    except Exception as error_send_error:
                        self.logger.error(f"[WebSocket] Failed to send error response: {error_send_error}")
                        break
                        
        except Exception as e:
            self.logger.error(f"[WebSocket] Handler error: {e}")
            import traceback
            self.logger.error(f"[WebSocket] Full traceback: {traceback.format_exc()}")
        finally:
            self.logger.info(f"[WebSocket] Connection closed for session {session_id}")


    async def _check_otp_success_and_update_ux(self, agent_id: str, user_message: str, agent_response: str) -> bool:
        """
        Check if OTP verification was successful and update UX_Command memory if needed.
        Returns True if OTP was successfully verified.
        """
        try:
            # Check if user message looks like an OTP (6 digits)
            import re
            if not re.match(r'^\d{6}$', user_message.strip()):
                return False
            
            # Check agent response for success indicators
            success_indicators = [
                "verification is complete",
                "verification complete", 
                "verified successfully",
                "phone number has been verified",
                "perfect! your verification is complete"
            ]
            
            agent_response_lower = agent_response.lower()
            otp_success = any(indicator in agent_response_lower for indicator in success_indicators)
            
            if otp_success:
                self.logger.info(f"[OTP] Success detected for agent {agent_id}")
                
                # Debug agent memory to see current state
                await self._debug_agent_memory(agent_id)
                
                # Extract user data from agent response FIRST (most reliable source)
                user_name = None
                user_phone = None
                
                # Look in the agent response itself for the data (most reliable)
                response_lines = agent_response.split('\n')
                for line in response_lines:
                    self.logger.debug(f"[OTP] Checking response line: {line}")
                    
                    # Extract name
                    if '• Name:' in line and not user_name:
                        name_part = line.split('• Name:')[1]
                        user_name = name_part.split('\n')[0].split(',')[0].strip()
                        self.logger.info(f"[OTP] Extracted name from response: '{user_name}'")
                    
                    # Extract phone
                    elif '• Phone:' in line and not user_phone:
                        phone_match = re.search(r'\+91\d{10}', line)
                        if phone_match:
                            user_phone = phone_match.group()
                            self.logger.info(f"[OTP] Extracted phone from response: '{user_phone}'")
                
                # If we didn't find name/phone in response, try scratch memory
                if not user_name or not user_phone:
                    self.logger.warning(f"[OTP] Could not find data in response. Name: {user_name}, Phone: {user_phone}")
                    self.logger.info(f"[OTP] Trying to extract from scratch memory...")
                    
                    try:
                        scratch_block = self.client.agents.blocks.retrieve(
                            agent_id=agent_id,
                            block_label="scratch"
                        )
                        
                        if scratch_block and scratch_block.value:
                            scratch_content = scratch_block.value
                            self.logger.info(f"[OTP] Scratch content: {scratch_content[:500]}...")
                            
                            # Parse scratch content for user info with multiple patterns
                            lines = scratch_content.split('\n')
                            for line in lines:
                                line = line.strip()
                                
                                # Try different patterns for name
                                if not user_name and any(pattern in line.lower() for pattern in ['user_name:', 'name:', 'full_name:']):
                                    if ':' in line:
                                        name_value = line.split(':', 1)[1].strip()
                                        if name_value and name_value.lower() not in ['null', 'none', '']:
                                            user_name = name_value
                                            self.logger.info(f"[OTP] Found name in scratch: {user_name}")
                                
                                # Try different patterns for phone
                                elif not user_phone and any(pattern in line.lower() for pattern in ['user_phone:', 'phone:', 'phone_number:']):
                                    if ':' in line:
                                        phone_value = line.split(':', 1)[1].strip()
                                        if phone_value and phone_value.lower() not in ['null', 'none', ''] and phone_value.startswith('+91'):
                                            user_phone = phone_value
                                            self.logger.info(f"[OTP] Found phone in scratch: {user_phone}")
                    
                    except Exception as scratch_error:
                        self.logger.warning(f"[OTP] Could not retrieve scratch block: {scratch_error}")
                
                # Create login data object with what we found
                from datetime import datetime
                login_data = {
                    'phone': user_phone if user_phone else 'unknown',
                    'name': user_name if user_name else 'User'
                }
                
                # Update UX_Command memory block with proper format
                ux_command_content = f"""# Login Data Object (populated after successful OTP verification)
login_data: {login_data}

# Authentication Status  
authentication_success: true
otp_verification_complete: true
ready_for_phase2: true

# UI State Management
active_validation_message: null
active_success_animation: "otp_verification_success"
active_error_message: null

# Phase Transition
phase_transition_ready: true
file_upload_enabled: true"""
                
                # Update the UX_Command memory block
                try:
                    self.client.agents.memory.update(
                        agent_id=agent_id,
                        memory_block_label="UX_Command",
                        value=ux_command_content
                    )
                    self.logger.info(f"[OTP] Successfully updated UX_Command memory with login data: {login_data}")
                except Exception as update_error:
                    self.logger.error(f"[OTP] Failed to update UX_Command memory: {update_error}")
                
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"[OTP] Error checking OTP success: {e}")
            import traceback
            self.logger.error(f"[OTP] Error traceback: {traceback.format_exc()}")
            return False

    async def _extract_login_data_from_agent(self, agent_id: str) -> dict:
        """Extract login data from agent's memory blocks using direct API calls"""
        try:
            self.logger.info(f"[LoginData] Starting extraction for agent {agent_id}")
            
            # First try to get login data from UX_Command memory block using direct API
            try:
                ux_block = self.client.agents.blocks.retrieve(
                    agent_id=agent_id,
                    block_label="UX_Command"
                )
                
                if ux_block and ux_block.value:
                    ux_content = ux_block.value
                    self.logger.info(f"[LoginData] UX_Command block retrieved: {ux_content[:300]}...")
                    
                    # Look for login_data pattern
                    import re
                    
                    # Try multiple patterns to find login_data
                    patterns = [
                        r'login_data:\s*({[^}]+})',
                        r'login_data:\s*(\{.*?\})',
                        r'login_data:\s*(\{.*?\n.*?\n.*?\})'
                    ]
                    
                    for pattern in patterns:
                        login_data_match = re.search(pattern, ux_content, re.DOTALL)
                        if login_data_match:
                            login_data_str = login_data_match.group(1)
                            self.logger.info(f"[LoginData] Found login_data string: {login_data_str}")
                            
                            try:
                                import ast
                                login_data = ast.literal_eval(login_data_str)
                                if login_data and isinstance(login_data, dict) and login_data.get('phone') not in ['unknown', None]:
                                    self.logger.info(f"[LoginData] Successfully parsed login_data from UX_Command: {login_data}")
                                    return login_data
                            except Exception as parse_error:
                                self.logger.warning(f"[LoginData] Failed to parse login_data: {parse_error}")
                                continue
                
            except Exception as ux_error:
                self.logger.warning(f"[LoginData] Could not retrieve UX_Command block: {ux_error}")
            
            # Fallback: extract from scratch memory block using direct API
            self.logger.info(f"[LoginData] Trying scratch memory block...")
            
            try:
                scratch_block = self.client.agents.blocks.retrieve(
                    agent_id=agent_id,
                    block_label="scratch"
                )
                
                if scratch_block and scratch_block.value:
                    scratch_content = scratch_block.value
                    self.logger.info(f"[LoginData] Scratch block retrieved: {scratch_content[:500]}...")
                    
                    user_name = None
                    user_phone = None
                    user_concerns = []
                    
                    # Parse scratch content for user info
                    lines = scratch_content.split('\n')
                    for line in lines:
                        line = line.strip()
                        
                        # Look for name patterns
                        if any(pattern in line.lower() for pattern in ['user_name:', 'name:', 'full_name:']):
                            if ':' in line:
                                name_value = line.split(':', 1)[1].strip()
                                if name_value and name_value.lower() not in ['null', 'none', '']:
                                    user_name = name_value
                                    self.logger.info(f"[LoginData] Found name in scratch: {user_name}")
                        
                        # Look for phone patterns
                        elif any(pattern in line.lower() for pattern in ['user_phone:', 'phone:', 'phone_number:']):
                            if ':' in line:
                                phone_value = line.split(':', 1)[1].strip()
                                if phone_value and phone_value.lower() not in ['null', 'none', ''] and phone_value.startswith('+91'):
                                    user_phone = phone_value
                                    self.logger.info(f"[LoginData] Found phone in scratch: {user_phone}")
                        
                        # Look for concerns
                        elif any(pattern in line.lower() for pattern in ['user_concerns:', 'concerns:', 'symptoms:']):
                            if ':' in line and '[' in line:
                                try:
                                    concerns_part = line.split(':', 1)[1].strip()
                                    if concerns_part.startswith('['):
                                        import ast
                                        user_concerns = ast.literal_eval(concerns_part)
                                        self.logger.info(f"[LoginData] Found concerns in scratch: {user_concerns}")
                                except Exception:
                                    pass
                    
                    # Create login data if we have the required info
                    if user_name and user_phone:
                        from datetime import datetime
                        login_data = {
                            'phone': user_phone,
                            'name': user_name
                        }
                        
                        if user_concerns:
                            login_data['concerns'] = user_concerns[:3]
                        
                        self.logger.info(f"[LoginData] Created login_data from scratch: {login_data}")
                        return login_data
                    else:
                        self.logger.warning(f"[LoginData] Insufficient data in scratch - name: {user_name}, phone: {user_phone}")
                
            except Exception as scratch_error:
                self.logger.warning(f"[LoginData] Could not retrieve scratch block: {scratch_error}")
            
            # If memory blocks didn't work, try to get it from the most recent agent messages
            self.logger.info(f"[LoginData] Memory blocks failed, trying recent messages...")
            
            try:
                recent_messages = self.client.agents.messages.list(
                    agent_id=agent_id,
                    limit=10,
                    use_assistant_message=True,
                    assistant_message_tool_name="send_message",
                    assistant_message_tool_kwarg="message"
                )
                
                # Look through recent messages for verification success response
                for msg in recent_messages:
                    if hasattr(msg, 'content'):
                        content = ""
                        if isinstance(msg.content, str):
                            content = msg.content
                        elif isinstance(msg.content, list) and len(msg.content) > 0:
                            for item in msg.content:
                                if isinstance(item, dict) and item.get('type') == 'text':
                                    content = item.get('text', '')
                                    break
                        
                        if content and 'verification is complete' in content.lower():
                            self.logger.info(f"[LoginData] Found verification message: {content[:200]}...")
                            
                            user_name = None
                            user_phone = None
                            
                            # Extract name and phone from this message
                            lines = content.split('\n')
                            for line in lines:
                                if '• Name:' in line and not user_name:
                                    name_part = line.split('• Name:')[1]
                                    user_name = name_part.split('\n')[0].split(',')[0].strip()
                                    self.logger.info(f"[LoginData] Extracted name from message: '{user_name}'")
                                
                                elif '• Phone:' in line and not user_phone:
                                    import re
                                    phone_match = re.search(r'\+91\d{10}', line)
                                    if phone_match:
                                        user_phone = phone_match.group()
                                        self.logger.info(f"[LoginData] Extracted phone from message: '{user_phone}'")
                            
                            if user_name and user_phone:
                                from datetime import datetime
                                login = {
                                    'phone': user_phone,
                                    'name': user_name,
                                    'source': 'recent_messages'
                                }
                                self.logger.info(f"[LoginData] Created login_data from messages: {login}")
                                return login
            
            except Exception as msg_error:
                self.logger.warning(f"[LoginData] Failed to get recent messages: {msg_error}")
            
            # Last resort: return None to trigger fallback
            self.logger.warning(f"[LoginData] All extraction methods failed")
            return None
            
        except Exception as e:
            self.logger.error(f"[LoginData] Error extracting login data: {e}")
            import traceback
            self.logger.error(f"[LoginData] Traceback: {traceback.format_exc()}")
            return None

    async def _debug_agent_memory(self, agent_id: str) -> str:
        """Debug method to log all agent memory blocks for troubleshooting"""
        try:
            # Use direct API to get memory blocks
            try:
                scratch_block = self.client.agents.blocks.retrieve(agent_id=agent_id, block_label="scratch")
                self.logger.info(f"[DEBUG] Scratch block content: {scratch_block.value[:300] if scratch_block else 'None'}...")
            except:
                self.logger.info(f"[DEBUG] Could not retrieve scratch block")
            
            try:
                ux_block = self.client.agents.blocks.retrieve(agent_id=agent_id, block_label="UX_Command")
                self.logger.info(f"[DEBUG] UX_Command block content: {ux_block.value[:300] if ux_block else 'None'}...")
            except:
                self.logger.info(f"[DEBUG] Could not retrieve UX_Command block")
            
            try:
                otp_block = self.client.agents.blocks.retrieve(agent_id=agent_id, block_label="otp")
                self.logger.info(f"[DEBUG] OTP block content: {otp_block.value[:300] if otp_block else 'None'}...")
            except:
                self.logger.info(f"[DEBUG] Could not retrieve OTP block")
                
            try:
                user_msg_block = self.client.agents.blocks.retrieve(agent_id=agent_id, block_label="user_message")
                self.logger.info(f"[DEBUG] User_message block content: {user_msg_block.value[:300] if user_msg_block else 'None'}...")
            except:
                self.logger.info(f"[DEBUG] Could not retrieve user_message block")
            
            return "Memory blocks logged"
            
        except Exception as e:
            error_msg = f"[DEBUG] Error debugging agent memory: {e}"
            self.logger.error(error_msg)
            return error_msg

    def _extract_user_message(self, data: Dict) -> str:
        """Extract user message from different possible keys"""
        possible_keys = ["message", "content", "text", "msg", "user_message"]
        for key in possible_keys:
            if key in data and data[key]:
                return data[key]
        return ""
    
    def get_all_sessions(self):
        """Get all session data including folder information and welcome status"""
        return {
            "total_sessions": len(self.session_data),
            "sessions": {
                sid: {
                    "agent_name": data["agent_name"],
                    "template_name": data["template_name"],
                    "category": data["category"],
                    "agent_type": data["agent_type"],
                    "phase": data["phase"],
                    "case_id": data.get("case_id"),
                    "user_id": data.get("user_id"),
                    "folders": data.get("folders", {}),
                    "attached_folders_count": len(data.get("attached_folders", [])),
                    "welcome_sent": data.get("welcome_sent", False)
                }
                for sid, data in self.session_data.items()
            }
        }
    
    def get_available_categories(self):
        """Get all available agent categories and their agents"""
        return {
            "categories": self.agent_categories,
            "total_categories": len(self.agent_categories),
            "total_templates": len(self.loaded_templates),
            "shared_memory_summary": list(self.shared_memory_blocks.keys()),
            "custom_tools": list(self.custom_tools.keys())
        }
    
    def get_all_templates(self):
        """Get all template information organized by category"""
        templates_by_category = {}
        for template_name, template_data in self.loaded_templates.items():
            category = template_data['metadata']['category']
            original_name = template_data['metadata']['original_name']
            
            if category not in templates_by_category:
                templates_by_category[category] = []
            
            agent_template = template_data.get('agent_template', {})
            templates_by_category[category].append({
                "name": original_name,
                "full_name": template_name,
                "display_name": agent_template.get('display_name', original_name),
                "description": agent_template.get('description', ''),
                "tags": template_data.get('metadata', {}).get('tags', []),
                "has_shared_memory": template_data['metadata'].get('has_shared_memory', False),
                "shared_memory_count": template_data['metadata'].get('shared_memory_count', 0)
            })
        
        return {
            "categories": templates_by_category,
            "total_templates": len(self.loaded_templates),
            "total_categories": len(templates_by_category)
        }
    
    def get_template_info(self, category: str, agent_name: str):
        """Get detailed template information"""
        template_name = f"{category}_{agent_name}"
        if template_name not in self.loaded_templates:
            return None
        
        template = self.loaded_templates[template_name]
        return {
            "category": category,
            "agent_name": agent_name,
            "full_name": template_name,
            "metadata": template.get('metadata', {}),
            "variables": template.get('variables', {}),
            "agent_template": {
                "name": template['agent_template'].get('name'),
                "display_name": template['agent_template'].get('display_name'),
                "description": template['agent_template'].get('description'),
                "tools": template['agent_template'].get('tools', []),
                "memory_blocks": [block.get('name', block.get('label', 'unnamed')) for block in template['agent_template'].get('memory_blocks', [])],
            },
            "shared_memory_blocks": list(template.get('shared_memory_blocks', {}).keys())
        }
    
    def reload_templates(self):
        """Reload all templates"""
        self.loaded_templates.clear()
        self.agent_categories.clear()
        self.shared_memory_blocks.clear()
        self._load_templates_from_folders()
        return {
            "success": True,
            "loaded_templates": list(self.loaded_templates.keys()),
            "categories": list(self.agent_categories.keys()),
            "shared_memory_blocks": list(self.shared_memory_blocks.keys()),
            "total_count": len(self.loaded_templates),
            "message": "Templates reloaded successfully"
        }
