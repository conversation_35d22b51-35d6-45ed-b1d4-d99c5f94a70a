# Healthcare Agent Creation Automation - Requirements
# Core dependencies for Letta agent factory and REST API

# Letta Client SDK for agent creation and management
letta-client>=0.1.220

# YAML processing
PyYAML>=6.0

# FastAPI framework (primary web framework)
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# REST API framework (legacy support)
Flask>=2.3.0
Flask-CORS>=4.0.0

# HTTP client for API calls
requests>=2.31.0

# Data processing and validation
pydantic>=2.0.0

# Date/time handling
python-dateutil>=2.8.0

# JSON processing (usually included with Python)
# json - built-in

# Path handling (usually included with Python)
# pathlib - built-in

# Type hints support
typing-extensions>=4.5.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0

# Optional: For enhanced logging and monitoring
loguru>=0.7.0

# Optional: For environment variable management
python-dotenv>=1.0.0
