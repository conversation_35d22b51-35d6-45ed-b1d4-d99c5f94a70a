# This file was auto-generated by Fern from our API Definition.

import typing

from ....types.update_assistant_message import UpdateAssistantMessage
from ....types.update_reasoning_message import UpdateReasoningMessage
from ....types.update_system_message import UpdateSystemMessage
from ....types.update_user_message import UpdateUserMessage

MessagesModifyRequest = typing.Union[
    UpdateSystemMessage, UpdateUserMessage, UpdateReasoningMessage, UpdateAssistantMessage
]
