# This file was auto-generated by Fern from our API Definition.

import typing

from ....types.assistant_message import AssistantMessage
from ....types.hidden_reasoning_message import HiddenReasoningMessage
from ....types.reasoning_message import ReasoningMessage
from ....types.system_message import SystemMessage
from ....types.tool_call_message import ToolCallMessage
from ....types.tool_return_message import ToolReturnMessage
from ....types.user_message import UserMessage

MessagesModifyResponse = typing.Union[
    SystemMessage,
    UserMessage,
    ReasoningMessage,
    HiddenReasoningMessage,
    ToolCallMessage,
    ToolReturnMessage,
    AssistantMessage,
]
