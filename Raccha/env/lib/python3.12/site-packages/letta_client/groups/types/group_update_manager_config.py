# This file was auto-generated by Fern from our API Definition.

import typing

from ...types.dynamic_manager_update import DynamicManagerUpdate
from ...types.round_robin_manager_update import RoundRobinManagerUpdate
from ...types.sleeptime_manager_update import SleeptimeManagerUpdate
from ...types.supervisor_manager_update import SupervisorManagerUpdate
from ...types.voice_sleeptime_manager_update import VoiceSleeptimeManagerUpdate

GroupUpdateManagerConfig = typing.Union[
    DynamicManagerUpdate,
    RoundRobinManagerUpdate,
    SleeptimeManagerUpdate,
    SupervisorManagerUpdate,
    VoiceSleeptimeManagerUpdate,
]
