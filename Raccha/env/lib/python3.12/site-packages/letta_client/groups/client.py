# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.group import Group
from ..types.manager_type import ManagerType
from .messages.client import Async<PERSON>essages<PERSON>lient, MessagesClient
from .raw_client import AsyncRawGroupsClient, RawGroupsClient
from .types.group_create_manager_config import GroupCreateManagerConfig
from .types.group_update_manager_config import GroupUpdateManagerConfig

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class GroupsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawGroupsClient(client_wrapper=client_wrapper)
        self.messages = MessagesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawGroupsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawGroupsClient
        """
        return self._raw_client

    def list(
        self,
        *,
        manager_type: typing.Optional[ManagerType] = None,
        before: typing.Optional[str] = None,
        after: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        project_id: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Group]:
        """
        Fetch all multi-agent groups matching query.

        Parameters
        ----------
        manager_type : typing.Optional[ManagerType]
            Search groups by manager type

        before : typing.Optional[str]
            Cursor for pagination

        after : typing.Optional[str]
            Cursor for pagination

        limit : typing.Optional[int]
            Limit for pagination

        project_id : typing.Optional[str]
            Search groups by project id

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Group]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.groups.list()
        """
        _response = self._raw_client.list(
            manager_type=manager_type,
            before=before,
            after=after,
            limit=limit,
            project_id=project_id,
            request_options=request_options,
        )
        return _response.data

    def create(
        self,
        *,
        agent_ids: typing.Sequence[str],
        description: str,
        manager_config: typing.Optional[GroupCreateManagerConfig] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        shared_block_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Group:
        """
        Create a new multi-agent group with the specified configuration.

        Parameters
        ----------
        agent_ids : typing.Sequence[str]


        description : str


        manager_config : typing.Optional[GroupCreateManagerConfig]


        project_id : typing.Optional[str]
            The associated project id.

        shared_block_ids : typing.Optional[typing.Sequence[str]]


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Group
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.groups.create(
            agent_ids=["agent_ids"],
            description="description",
        )
        """
        _response = self._raw_client.create(
            agent_ids=agent_ids,
            description=description,
            manager_config=manager_config,
            project_id=project_id,
            shared_block_ids=shared_block_ids,
            request_options=request_options,
        )
        return _response.data

    def count(self, *, request_options: typing.Optional[RequestOptions] = None) -> int:
        """
        Get the count of all groups associated with a given user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        int
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.groups.count()
        """
        _response = self._raw_client.count(request_options=request_options)
        return _response.data

    def retrieve(self, group_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> Group:
        """
        Retrieve the group by id.

        Parameters
        ----------
        group_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Group
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.groups.retrieve(
            group_id="group_id",
        )
        """
        _response = self._raw_client.retrieve(group_id, request_options=request_options)
        return _response.data

    def delete(
        self, group_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a multi-agent group.

        Parameters
        ----------
        group_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.groups.delete(
            group_id="group_id",
        )
        """
        _response = self._raw_client.delete(group_id, request_options=request_options)
        return _response.data

    def modify(
        self,
        group_id: str,
        *,
        agent_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        description: typing.Optional[str] = OMIT,
        manager_config: typing.Optional[GroupUpdateManagerConfig] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        shared_block_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Group:
        """
        Create a new multi-agent group with the specified configuration.

        Parameters
        ----------
        group_id : str

        agent_ids : typing.Optional[typing.Sequence[str]]


        description : typing.Optional[str]


        manager_config : typing.Optional[GroupUpdateManagerConfig]


        project_id : typing.Optional[str]
            The associated project id.

        shared_block_ids : typing.Optional[typing.Sequence[str]]


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Group
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.groups.modify(
            group_id="group_id",
        )
        """
        _response = self._raw_client.modify(
            group_id,
            agent_ids=agent_ids,
            description=description,
            manager_config=manager_config,
            project_id=project_id,
            shared_block_ids=shared_block_ids,
            request_options=request_options,
        )
        return _response.data


class AsyncGroupsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawGroupsClient(client_wrapper=client_wrapper)
        self.messages = AsyncMessagesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawGroupsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawGroupsClient
        """
        return self._raw_client

    async def list(
        self,
        *,
        manager_type: typing.Optional[ManagerType] = None,
        before: typing.Optional[str] = None,
        after: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        project_id: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Group]:
        """
        Fetch all multi-agent groups matching query.

        Parameters
        ----------
        manager_type : typing.Optional[ManagerType]
            Search groups by manager type

        before : typing.Optional[str]
            Cursor for pagination

        after : typing.Optional[str]
            Cursor for pagination

        limit : typing.Optional[int]
            Limit for pagination

        project_id : typing.Optional[str]
            Search groups by project id

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Group]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.groups.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            manager_type=manager_type,
            before=before,
            after=after,
            limit=limit,
            project_id=project_id,
            request_options=request_options,
        )
        return _response.data

    async def create(
        self,
        *,
        agent_ids: typing.Sequence[str],
        description: str,
        manager_config: typing.Optional[GroupCreateManagerConfig] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        shared_block_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Group:
        """
        Create a new multi-agent group with the specified configuration.

        Parameters
        ----------
        agent_ids : typing.Sequence[str]


        description : str


        manager_config : typing.Optional[GroupCreateManagerConfig]


        project_id : typing.Optional[str]
            The associated project id.

        shared_block_ids : typing.Optional[typing.Sequence[str]]


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Group
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.groups.create(
                agent_ids=["agent_ids"],
                description="description",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            agent_ids=agent_ids,
            description=description,
            manager_config=manager_config,
            project_id=project_id,
            shared_block_ids=shared_block_ids,
            request_options=request_options,
        )
        return _response.data

    async def count(self, *, request_options: typing.Optional[RequestOptions] = None) -> int:
        """
        Get the count of all groups associated with a given user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        int
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.groups.count()


        asyncio.run(main())
        """
        _response = await self._raw_client.count(request_options=request_options)
        return _response.data

    async def retrieve(self, group_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> Group:
        """
        Retrieve the group by id.

        Parameters
        ----------
        group_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Group
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.groups.retrieve(
                group_id="group_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.retrieve(group_id, request_options=request_options)
        return _response.data

    async def delete(
        self, group_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a multi-agent group.

        Parameters
        ----------
        group_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.groups.delete(
                group_id="group_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(group_id, request_options=request_options)
        return _response.data

    async def modify(
        self,
        group_id: str,
        *,
        agent_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        description: typing.Optional[str] = OMIT,
        manager_config: typing.Optional[GroupUpdateManagerConfig] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        shared_block_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Group:
        """
        Create a new multi-agent group with the specified configuration.

        Parameters
        ----------
        group_id : str

        agent_ids : typing.Optional[typing.Sequence[str]]


        description : typing.Optional[str]


        manager_config : typing.Optional[GroupUpdateManagerConfig]


        project_id : typing.Optional[str]
            The associated project id.

        shared_block_ids : typing.Optional[typing.Sequence[str]]


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Group
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.groups.modify(
                group_id="group_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.modify(
            group_id,
            agent_ids=agent_ids,
            description=description,
            manager_config=manager_config,
            project_id=project_id,
            shared_block_ids=shared_block_ids,
            request_options=request_options,
        )
        return _response.data
