# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import <PERSON><PERSON><PERSON><PERSON><PERSON>rapper, Sync<PERSON><PERSON>Wrapper
from ..core.request_options import RequestOptions
from ..types.batch_job import Batch<PERSON>ob
from ..types.letta_batch_request import LettaBatchRequest
from .raw_client import Async<PERSON>aw<PERSON><PERSON><PERSON><PERSON>lient, RawBatchesClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class BatchesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawBatchesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawBatchesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawBatchesClient
        """
        return self._raw_client

    def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> typing.List[BatchJob]:
        """
        List all batch runs.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[BatchJob]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.batches.list()
        """
        _response = self._raw_client.list(request_options=request_options)
        return _response.data

    def create(
        self,
        *,
        requests: typing.Sequence[LettaBatchRequest],
        callback_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> BatchJob:
        """
        Submit a batch of agent messages for asynchronous processing.
        Creates a job that will fan out messages to all listed agents and process them in parallel.

        Parameters
        ----------
        requests : typing.Sequence[LettaBatchRequest]
            List of requests to be processed in batch.

        callback_url : typing.Optional[str]
            Optional URL to call via POST when the batch completes. The callback payload will be a JSON object with the following fields: {'job_id': string, 'status': string, 'completed_at': string}. Where 'job_id' is the unique batch job identifier, 'status' is the final batch status (e.g., 'completed', 'failed'), and 'completed_at' is an ISO 8601 timestamp indicating when the batch job completed.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        BatchJob
            Successful Response

        Examples
        --------
        from letta_client import Letta, LettaBatchRequest, MessageCreate, TextContent

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.batches.create(
            requests=[
                LettaBatchRequest(
                    messages=[
                        MessageCreate(
                            role="user",
                            content=[
                                TextContent(
                                    text="text",
                                )
                            ],
                        )
                    ],
                    agent_id="agent_id",
                )
            ],
        )
        """
        _response = self._raw_client.create(
            requests=requests, callback_url=callback_url, request_options=request_options
        )
        return _response.data

    def retrieve(self, batch_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> BatchJob:
        """
        Get the status of a batch run.

        Parameters
        ----------
        batch_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        BatchJob
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.batches.retrieve(
            batch_id="batch_id",
        )
        """
        _response = self._raw_client.retrieve(batch_id, request_options=request_options)
        return _response.data

    def cancel(
        self, batch_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Cancel a batch run.

        Parameters
        ----------
        batch_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.batches.cancel(
            batch_id="batch_id",
        )
        """
        _response = self._raw_client.cancel(batch_id, request_options=request_options)
        return _response.data


class AsyncBatchesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawBatchesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawBatchesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawBatchesClient
        """
        return self._raw_client

    async def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> typing.List[BatchJob]:
        """
        List all batch runs.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[BatchJob]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.batches.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(request_options=request_options)
        return _response.data

    async def create(
        self,
        *,
        requests: typing.Sequence[LettaBatchRequest],
        callback_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> BatchJob:
        """
        Submit a batch of agent messages for asynchronous processing.
        Creates a job that will fan out messages to all listed agents and process them in parallel.

        Parameters
        ----------
        requests : typing.Sequence[LettaBatchRequest]
            List of requests to be processed in batch.

        callback_url : typing.Optional[str]
            Optional URL to call via POST when the batch completes. The callback payload will be a JSON object with the following fields: {'job_id': string, 'status': string, 'completed_at': string}. Where 'job_id' is the unique batch job identifier, 'status' is the final batch status (e.g., 'completed', 'failed'), and 'completed_at' is an ISO 8601 timestamp indicating when the batch job completed.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        BatchJob
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import (
            AsyncLetta,
            LettaBatchRequest,
            MessageCreate,
            TextContent,
        )

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.batches.create(
                requests=[
                    LettaBatchRequest(
                        messages=[
                            MessageCreate(
                                role="user",
                                content=[
                                    TextContent(
                                        text="text",
                                    )
                                ],
                            )
                        ],
                        agent_id="agent_id",
                    )
                ],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            requests=requests, callback_url=callback_url, request_options=request_options
        )
        return _response.data

    async def retrieve(self, batch_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> BatchJob:
        """
        Get the status of a batch run.

        Parameters
        ----------
        batch_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        BatchJob
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.batches.retrieve(
                batch_id="batch_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.retrieve(batch_id, request_options=request_options)
        return _response.data

    async def cancel(
        self, batch_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Cancel a batch run.

        Parameters
        ----------
        batch_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.batches.cancel(
                batch_id="batch_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.cancel(batch_id, request_options=request_options)
        return _response.data
