# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.block import Block
from .agents.client import Agents<PERSON>lient, AsyncAgentsClient
from .raw_client import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lient, RawBlocksClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class BlocksClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawBlocksClient(client_wrapper=client_wrapper)
        self.agents = AgentsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawBlocksClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawBlocksClient
        """
        return self._raw_client

    def list(
        self,
        *,
        label: typing.Optional[str] = None,
        templates_only: typing.Optional[bool] = None,
        name: typing.Optional[str] = None,
        identity_id: typing.Optional[str] = None,
        identifier_keys: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        project_id: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        before: typing.Optional[str] = None,
        after: typing.Optional[str] = None,
        label_search: typing.Optional[str] = None,
        description_search: typing.Optional[str] = None,
        value_search: typing.Optional[str] = None,
        connected_to_agents_count_gt: typing.Optional[int] = None,
        connected_to_agents_count_lt: typing.Optional[int] = None,
        connected_to_agents_count_eq: typing.Optional[typing.Union[int, typing.Sequence[int]]] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Block]:
        """
        Parameters
        ----------
        label : typing.Optional[str]
            Labels to include (e.g. human, persona)

        templates_only : typing.Optional[bool]
            Whether to include only templates

        name : typing.Optional[str]
            Name of the block

        identity_id : typing.Optional[str]
            Search agents by identifier id

        identifier_keys : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            Search agents by identifier keys

        project_id : typing.Optional[str]
            Search blocks by project id

        limit : typing.Optional[int]
            Number of blocks to return

        before : typing.Optional[str]
            Cursor for pagination. If provided, returns blocks before this cursor.

        after : typing.Optional[str]
            Cursor for pagination. If provided, returns blocks after this cursor.

        label_search : typing.Optional[str]
            Search blocks by label. If provided, returns blocks that match this label. This is a full-text search on labels.

        description_search : typing.Optional[str]
            Search blocks by description. If provided, returns blocks that match this description. This is a full-text search on block descriptions.

        value_search : typing.Optional[str]
            Search blocks by value. If provided, returns blocks that match this value.

        connected_to_agents_count_gt : typing.Optional[int]
            Filter blocks by the number of connected agents. If provided, returns blocks that have more than this number of connected agents.

        connected_to_agents_count_lt : typing.Optional[int]
            Filter blocks by the number of connected agents. If provided, returns blocks that have less than this number of connected agents.

        connected_to_agents_count_eq : typing.Optional[typing.Union[int, typing.Sequence[int]]]
            Filter blocks by the exact number of connected agents. If provided, returns blocks that have exactly this number of connected agents.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Block]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.blocks.list()
        """
        _response = self._raw_client.list(
            label=label,
            templates_only=templates_only,
            name=name,
            identity_id=identity_id,
            identifier_keys=identifier_keys,
            project_id=project_id,
            limit=limit,
            before=before,
            after=after,
            label_search=label_search,
            description_search=description_search,
            value_search=value_search,
            connected_to_agents_count_gt=connected_to_agents_count_gt,
            connected_to_agents_count_lt=connected_to_agents_count_lt,
            connected_to_agents_count_eq=connected_to_agents_count_eq,
            request_options=request_options,
        )
        return _response.data

    def create(
        self,
        *,
        value: str,
        label: str,
        limit: typing.Optional[int] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        name: typing.Optional[str] = OMIT,
        is_template: typing.Optional[bool] = OMIT,
        preserve_on_migration: typing.Optional[bool] = OMIT,
        read_only: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Block:
        """
        Parameters
        ----------
        value : str
            Value of the block.

        label : str
            Label of the block.

        limit : typing.Optional[int]
            Character limit of the block.

        project_id : typing.Optional[str]
            The associated project id.

        name : typing.Optional[str]
            Name of the block if it is a template.

        is_template : typing.Optional[bool]

        preserve_on_migration : typing.Optional[bool]
            Preserve the block on template migration.

        read_only : typing.Optional[bool]
            Whether the agent has read-only access to the block.

        description : typing.Optional[str]
            Description of the block.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata of the block.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.blocks.create(
            value="value",
            label="label",
        )
        """
        _response = self._raw_client.create(
            value=value,
            label=label,
            limit=limit,
            project_id=project_id,
            name=name,
            is_template=is_template,
            preserve_on_migration=preserve_on_migration,
            read_only=read_only,
            description=description,
            metadata=metadata,
            request_options=request_options,
        )
        return _response.data

    def count(self, *, request_options: typing.Optional[RequestOptions] = None) -> int:
        """
        Count all blocks created by a user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        int
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.blocks.count()
        """
        _response = self._raw_client.count(request_options=request_options)
        return _response.data

    def retrieve(self, block_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> Block:
        """
        Parameters
        ----------
        block_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.blocks.retrieve(
            block_id="block_id",
        )
        """
        _response = self._raw_client.retrieve(block_id, request_options=request_options)
        return _response.data

    def delete(
        self, block_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Parameters
        ----------
        block_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.blocks.delete(
            block_id="block_id",
        )
        """
        _response = self._raw_client.delete(block_id, request_options=request_options)
        return _response.data

    def modify(
        self,
        block_id: str,
        *,
        value: typing.Optional[str] = OMIT,
        limit: typing.Optional[int] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        name: typing.Optional[str] = OMIT,
        is_template: typing.Optional[bool] = OMIT,
        preserve_on_migration: typing.Optional[bool] = OMIT,
        label: typing.Optional[str] = OMIT,
        read_only: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Block:
        """
        Parameters
        ----------
        block_id : str

        value : typing.Optional[str]
            Value of the block.

        limit : typing.Optional[int]
            Character limit of the block.

        project_id : typing.Optional[str]
            The associated project id.

        name : typing.Optional[str]
            Name of the block if it is a template.

        is_template : typing.Optional[bool]
            Whether the block is a template (e.g. saved human/persona options).

        preserve_on_migration : typing.Optional[bool]
            Preserve the block on template migration.

        label : typing.Optional[str]
            Label of the block (e.g. 'human', 'persona') in the context window.

        read_only : typing.Optional[bool]
            Whether the agent has read-only access to the block.

        description : typing.Optional[str]
            Description of the block.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata of the block.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.blocks.modify(
            block_id="block_id",
        )
        """
        _response = self._raw_client.modify(
            block_id,
            value=value,
            limit=limit,
            project_id=project_id,
            name=name,
            is_template=is_template,
            preserve_on_migration=preserve_on_migration,
            label=label,
            read_only=read_only,
            description=description,
            metadata=metadata,
            request_options=request_options,
        )
        return _response.data


class AsyncBlocksClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawBlocksClient(client_wrapper=client_wrapper)
        self.agents = AsyncAgentsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawBlocksClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawBlocksClient
        """
        return self._raw_client

    async def list(
        self,
        *,
        label: typing.Optional[str] = None,
        templates_only: typing.Optional[bool] = None,
        name: typing.Optional[str] = None,
        identity_id: typing.Optional[str] = None,
        identifier_keys: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        project_id: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        before: typing.Optional[str] = None,
        after: typing.Optional[str] = None,
        label_search: typing.Optional[str] = None,
        description_search: typing.Optional[str] = None,
        value_search: typing.Optional[str] = None,
        connected_to_agents_count_gt: typing.Optional[int] = None,
        connected_to_agents_count_lt: typing.Optional[int] = None,
        connected_to_agents_count_eq: typing.Optional[typing.Union[int, typing.Sequence[int]]] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Block]:
        """
        Parameters
        ----------
        label : typing.Optional[str]
            Labels to include (e.g. human, persona)

        templates_only : typing.Optional[bool]
            Whether to include only templates

        name : typing.Optional[str]
            Name of the block

        identity_id : typing.Optional[str]
            Search agents by identifier id

        identifier_keys : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            Search agents by identifier keys

        project_id : typing.Optional[str]
            Search blocks by project id

        limit : typing.Optional[int]
            Number of blocks to return

        before : typing.Optional[str]
            Cursor for pagination. If provided, returns blocks before this cursor.

        after : typing.Optional[str]
            Cursor for pagination. If provided, returns blocks after this cursor.

        label_search : typing.Optional[str]
            Search blocks by label. If provided, returns blocks that match this label. This is a full-text search on labels.

        description_search : typing.Optional[str]
            Search blocks by description. If provided, returns blocks that match this description. This is a full-text search on block descriptions.

        value_search : typing.Optional[str]
            Search blocks by value. If provided, returns blocks that match this value.

        connected_to_agents_count_gt : typing.Optional[int]
            Filter blocks by the number of connected agents. If provided, returns blocks that have more than this number of connected agents.

        connected_to_agents_count_lt : typing.Optional[int]
            Filter blocks by the number of connected agents. If provided, returns blocks that have less than this number of connected agents.

        connected_to_agents_count_eq : typing.Optional[typing.Union[int, typing.Sequence[int]]]
            Filter blocks by the exact number of connected agents. If provided, returns blocks that have exactly this number of connected agents.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Block]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.blocks.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            label=label,
            templates_only=templates_only,
            name=name,
            identity_id=identity_id,
            identifier_keys=identifier_keys,
            project_id=project_id,
            limit=limit,
            before=before,
            after=after,
            label_search=label_search,
            description_search=description_search,
            value_search=value_search,
            connected_to_agents_count_gt=connected_to_agents_count_gt,
            connected_to_agents_count_lt=connected_to_agents_count_lt,
            connected_to_agents_count_eq=connected_to_agents_count_eq,
            request_options=request_options,
        )
        return _response.data

    async def create(
        self,
        *,
        value: str,
        label: str,
        limit: typing.Optional[int] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        name: typing.Optional[str] = OMIT,
        is_template: typing.Optional[bool] = OMIT,
        preserve_on_migration: typing.Optional[bool] = OMIT,
        read_only: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Block:
        """
        Parameters
        ----------
        value : str
            Value of the block.

        label : str
            Label of the block.

        limit : typing.Optional[int]
            Character limit of the block.

        project_id : typing.Optional[str]
            The associated project id.

        name : typing.Optional[str]
            Name of the block if it is a template.

        is_template : typing.Optional[bool]

        preserve_on_migration : typing.Optional[bool]
            Preserve the block on template migration.

        read_only : typing.Optional[bool]
            Whether the agent has read-only access to the block.

        description : typing.Optional[str]
            Description of the block.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata of the block.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.blocks.create(
                value="value",
                label="label",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            value=value,
            label=label,
            limit=limit,
            project_id=project_id,
            name=name,
            is_template=is_template,
            preserve_on_migration=preserve_on_migration,
            read_only=read_only,
            description=description,
            metadata=metadata,
            request_options=request_options,
        )
        return _response.data

    async def count(self, *, request_options: typing.Optional[RequestOptions] = None) -> int:
        """
        Count all blocks created by a user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        int
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.blocks.count()


        asyncio.run(main())
        """
        _response = await self._raw_client.count(request_options=request_options)
        return _response.data

    async def retrieve(self, block_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> Block:
        """
        Parameters
        ----------
        block_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.blocks.retrieve(
                block_id="block_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.retrieve(block_id, request_options=request_options)
        return _response.data

    async def delete(
        self, block_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Parameters
        ----------
        block_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.blocks.delete(
                block_id="block_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(block_id, request_options=request_options)
        return _response.data

    async def modify(
        self,
        block_id: str,
        *,
        value: typing.Optional[str] = OMIT,
        limit: typing.Optional[int] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        name: typing.Optional[str] = OMIT,
        is_template: typing.Optional[bool] = OMIT,
        preserve_on_migration: typing.Optional[bool] = OMIT,
        label: typing.Optional[str] = OMIT,
        read_only: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Block:
        """
        Parameters
        ----------
        block_id : str

        value : typing.Optional[str]
            Value of the block.

        limit : typing.Optional[int]
            Character limit of the block.

        project_id : typing.Optional[str]
            The associated project id.

        name : typing.Optional[str]
            Name of the block if it is a template.

        is_template : typing.Optional[bool]
            Whether the block is a template (e.g. saved human/persona options).

        preserve_on_migration : typing.Optional[bool]
            Preserve the block on template migration.

        label : typing.Optional[str]
            Label of the block (e.g. 'human', 'persona') in the context window.

        read_only : typing.Optional[bool]
            Whether the agent has read-only access to the block.

        description : typing.Optional[str]
            Description of the block.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata of the block.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.blocks.modify(
                block_id="block_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.modify(
            block_id,
            value=value,
            limit=limit,
            project_id=project_id,
            name=name,
            is_template=is_template,
            preserve_on_migration=preserve_on_migration,
            label=label,
            read_only=read_only,
            description=description,
            metadata=metadata,
            request_options=request_options,
        )
        return _response.data
