# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncC<PERSON>Wrapper
from ...core.request_options import RequestOptions
from ...types.identity_property import IdentityProperty
from .raw_client import AsyncRawPropertiesClient, RawPropertiesClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class PropertiesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawPropertiesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawPropertiesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawPropertiesClient
        """
        return self._raw_client

    def upsert(
        self,
        identity_id: str,
        *,
        request: typing.Sequence[IdentityProperty],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Parameters
        ----------
        identity_id : str

        request : typing.Sequence[IdentityProperty]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from letta_client import IdentityProperty, Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.identities.properties.upsert(
            identity_id="identity_id",
            request=[
                IdentityProperty(
                    key="key",
                    value="value",
                    type="string",
                )
            ],
        )
        """
        _response = self._raw_client.upsert(identity_id, request=request, request_options=request_options)
        return _response.data


class AsyncPropertiesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawPropertiesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawPropertiesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawPropertiesClient
        """
        return self._raw_client

    async def upsert(
        self,
        identity_id: str,
        *,
        request: typing.Sequence[IdentityProperty],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Parameters
        ----------
        identity_id : str

        request : typing.Sequence[IdentityProperty]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta, IdentityProperty

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.identities.properties.upsert(
                identity_id="identity_id",
                request=[
                    IdentityProperty(
                        key="key",
                        value="value",
                        type="string",
                    )
                ],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.upsert(identity_id, request=request, request_options=request_options)
        return _response.data
