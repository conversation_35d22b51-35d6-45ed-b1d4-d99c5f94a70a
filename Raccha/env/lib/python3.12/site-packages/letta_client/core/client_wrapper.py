# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import httpx
from .http_client import AsyncHttpClient, HttpClient


class BaseClientWrapper:
    def __init__(
        self,
        *,
        project: typing.Optional[str] = None,
        token: typing.Optional[str] = None,
        headers: typing.Optional[typing.Dict[str, str]] = None,
        base_url: str,
        timeout: typing.Optional[float] = None,
    ):
        self._project = project
        self.token = token
        self._headers = headers
        self._base_url = base_url
        self._timeout = timeout

    def get_headers(self) -> typing.Dict[str, str]:
        headers: typing.Dict[str, str] = {
            "User-Agent": "letta-client/0.1.277",
            "X-Fern-Language": "Python",
            "X-Fern-SDK-Name": "letta-client",
            "X-Fern-SDK-Version": "0.1.277",
            **(self.get_custom_headers() or {}),
        }
        if self._project is not None:
            headers["X-Project"] = self._project
        if self.token is not None:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers

    def get_custom_headers(self) -> typing.Optional[typing.Dict[str, str]]:
        return self._headers

    def get_base_url(self) -> str:
        return self._base_url

    def get_timeout(self) -> typing.Optional[float]:
        return self._timeout


class SyncClientWrapper(BaseClientWrapper):
    def __init__(
        self,
        *,
        project: typing.Optional[str] = None,
        token: typing.Optional[str] = None,
        headers: typing.Optional[typing.Dict[str, str]] = None,
        base_url: str,
        timeout: typing.Optional[float] = None,
        httpx_client: httpx.Client,
    ):
        super().__init__(project=project, token=token, headers=headers, base_url=base_url, timeout=timeout)
        self.httpx_client = HttpClient(
            httpx_client=httpx_client,
            base_headers=self.get_headers,
            base_timeout=self.get_timeout,
            base_url=self.get_base_url,
        )


class AsyncClientWrapper(BaseClientWrapper):
    def __init__(
        self,
        *,
        project: typing.Optional[str] = None,
        token: typing.Optional[str] = None,
        headers: typing.Optional[typing.Dict[str, str]] = None,
        base_url: str,
        timeout: typing.Optional[float] = None,
        httpx_client: httpx.AsyncClient,
    ):
        super().__init__(project=project, token=token, headers=headers, base_url=base_url, timeout=timeout)
        self.httpx_client = AsyncHttpClient(
            httpx_client=httpx_client,
            base_headers=self.get_headers,
            base_timeout=self.get_timeout,
            base_url=self.get_base_url,
        )
