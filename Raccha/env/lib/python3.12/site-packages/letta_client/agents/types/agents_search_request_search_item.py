# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .agents_search_request_search_item_field import AgentsSearchRequestSearchItemField
from .agents_search_request_search_item_one import AgentsSearchRequestSearchItemOne
from .agents_search_request_search_item_three import AgentsSearchRequestSearchItemThree
from .agents_search_request_search_item_two import AgentsSearchRequestSearchItemTwo
from .agents_search_request_search_item_zero import AgentsSearchRequestSearchItemZero

AgentsSearchRequestSearchItem = typing.Union[
    AgentsSearchRequestSearchItemZero,
    AgentsSearchRequestSearchItemOne,
    AgentsSearchRequestSearchItemTwo,
    AgentsSearchRequestSearchItemThree,
    AgentsSearchRequestSearchItemField,
]
