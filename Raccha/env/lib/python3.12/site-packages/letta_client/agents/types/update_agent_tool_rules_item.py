# This file was auto-generated by Fern from our API Definition.

import typing

from ...types.child_tool_rule import ChildToolRule
from ...types.conditional_tool_rule import ConditionalToolRule
from ...types.continue_tool_rule import ContinueToolRule
from ...types.init_tool_rule import InitToolRule
from ...types.max_count_per_step_tool_rule import MaxCountPerStepToolRule
from ...types.parent_tool_rule import ParentToolRule
from ...types.required_before_exit_tool_rule import RequiredBeforeExitToolRule
from ...types.terminal_tool_rule import TerminalToolRule

UpdateAgentToolRulesItem = typing.Union[
    ConditionalToolRule,
    ChildToolRule,
    ContinueToolRule,
    TerminalToolRule,
    MaxCountPerStepToolRule,
    ParentToolRule,
    RequiredBeforeExitToolRule,
    InitToolRule,
]
