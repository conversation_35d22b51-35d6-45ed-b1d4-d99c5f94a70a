# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, Sync<PERSON><PERSON>Wrapper
from ...core.request_options import RequestOptions
from ...types.context_window_overview import ContextWindowOverview
from .raw_client import Async<PERSON>aw<PERSON>ontextClient, RawContextClient


class ContextClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawContextClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawContextClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawContextClient
        """
        return self._raw_client

    def retrieve(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ContextWindowOverview:
        """
        Retrieve the context window of a specific agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ContextWindowOverview
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.context.retrieve(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.retrieve(agent_id, request_options=request_options)
        return _response.data


class AsyncContextClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawContextClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawContextClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawContextClient
        """
        return self._raw_client

    async def retrieve(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ContextWindowOverview:
        """
        Retrieve the context window of a specific agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ContextWindowOverview
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.context.retrieve(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.retrieve(agent_id, request_options=request_options)
        return _response.data
