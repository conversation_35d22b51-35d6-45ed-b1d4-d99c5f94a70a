# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from .. import core
from ..core.client_wrapper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.agent_state import AgentState
from ..types.agent_type import AgentType
from ..types.create_block import <PERSON><PERSON><PERSON><PERSON>
from ..types.embedding_config import EmbeddingConfig
from ..types.imported_agents_response import ImportedAgentsResponse
from ..types.llm_config import LlmConfig
from ..types.message_create import MessageCreate
from ..types.paginated_agent_files import PaginatedAgentFiles
from .blocks.client import AsyncBlocksClient, BlocksClient
from .context.client import AsyncContextClient, ContextClient
from .core_memory.client import AsyncCoreMemoryClient, CoreMemoryClient
from .files.client import AsyncFilesClient, FilesClient
from .folders.client import AsyncFoldersClient, FoldersClient
from .groups.client import AsyncGroups<PERSON>lient, GroupsClient
from .memory_variables.client import Async<PERSON>emoryVariables<PERSON>lient, MemoryVariablesClient
from .messages.client import As<PERSON><PERSON><PERSON><PERSON><PERSON>lient, MessagesClient
from .passages.client import Async<PERSON><PERSON>ages<PERSON>lient, Passages<PERSON>lient
from .raw_client import AsyncRawAgentsClient, RawAgentsClient
from .sources.client import AsyncSourcesClient, SourcesClient
from .templates.client import AsyncTemplatesClient, TemplatesClient
from .tools.client import AsyncToolsClient, ToolsClient
from .types.agents_search_request_search_item import AgentsSearchRequestSearchItem
from .types.agents_search_request_sort_by import AgentsSearchRequestSortBy
from .types.agents_search_response import AgentsSearchResponse
from .types.create_agent_request_response_format import CreateAgentRequestResponseFormat
from .types.create_agent_request_tool_rules_item import CreateAgentRequestToolRulesItem
from .types.update_agent_response_format import UpdateAgentResponseFormat
from .types.update_agent_tool_rules_item import UpdateAgentToolRulesItem

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class AgentsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawAgentsClient(client_wrapper=client_wrapper)
        self.context = ContextClient(client_wrapper=client_wrapper)

        self.tools = ToolsClient(client_wrapper=client_wrapper)

        self.sources = SourcesClient(client_wrapper=client_wrapper)

        self.folders = FoldersClient(client_wrapper=client_wrapper)

        self.files = FilesClient(client_wrapper=client_wrapper)

        self.core_memory = CoreMemoryClient(client_wrapper=client_wrapper)

        self.blocks = BlocksClient(client_wrapper=client_wrapper)

        self.passages = PassagesClient(client_wrapper=client_wrapper)

        self.messages = MessagesClient(client_wrapper=client_wrapper)

        self.groups = GroupsClient(client_wrapper=client_wrapper)

        self.templates = TemplatesClient(client_wrapper=client_wrapper)

        self.memory_variables = MemoryVariablesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawAgentsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawAgentsClient
        """
        return self._raw_client

    def list(
        self,
        *,
        name: typing.Optional[str] = None,
        tags: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        match_all_tags: typing.Optional[bool] = None,
        before: typing.Optional[str] = None,
        after: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        query_text: typing.Optional[str] = None,
        project_id: typing.Optional[str] = None,
        template_id: typing.Optional[str] = None,
        base_template_id: typing.Optional[str] = None,
        identity_id: typing.Optional[str] = None,
        identifier_keys: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        include_relationships: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        ascending: typing.Optional[bool] = None,
        sort_by: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[AgentState]:
        """
        List all agents associated with a given user.

        This endpoint retrieves a list of all agents and their configurations
        associated with the specified user ID.

        Parameters
        ----------
        name : typing.Optional[str]
            Name of the agent

        tags : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            List of tags to filter agents by

        match_all_tags : typing.Optional[bool]
            If True, only returns agents that match ALL given tags. Otherwise, return agents that have ANY of the passed-in tags.

        before : typing.Optional[str]
            Cursor for pagination

        after : typing.Optional[str]
            Cursor for pagination

        limit : typing.Optional[int]
            Limit for pagination

        query_text : typing.Optional[str]
            Search agents by name

        project_id : typing.Optional[str]
            Search agents by project ID - this will default to your default project on cloud

        template_id : typing.Optional[str]
            Search agents by template ID

        base_template_id : typing.Optional[str]
            Search agents by base template ID

        identity_id : typing.Optional[str]
            Search agents by identity ID

        identifier_keys : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            Search agents by identifier keys

        include_relationships : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            Specify which relational fields (e.g., 'tools', 'sources', 'memory') to include in the response. If not provided, all relationships are loaded by default. Using this can optimize performance by reducing unnecessary joins.

        ascending : typing.Optional[bool]
            Whether to sort agents oldest to newest (True) or newest to oldest (False, default)

        sort_by : typing.Optional[str]
            Field to sort by. Options: 'created_at' (default), 'last_run_completion'

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[AgentState]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.list()
        """
        _response = self._raw_client.list(
            name=name,
            tags=tags,
            match_all_tags=match_all_tags,
            before=before,
            after=after,
            limit=limit,
            query_text=query_text,
            project_id=project_id,
            template_id=template_id,
            base_template_id=base_template_id,
            identity_id=identity_id,
            identifier_keys=identifier_keys,
            include_relationships=include_relationships,
            ascending=ascending,
            sort_by=sort_by,
            request_options=request_options,
        )
        return _response.data

    def create(
        self,
        *,
        name: typing.Optional[str] = OMIT,
        memory_blocks: typing.Optional[typing.Sequence[CreateBlock]] = OMIT,
        tools: typing.Optional[typing.Sequence[str]] = OMIT,
        tool_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        source_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        block_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        tool_rules: typing.Optional[typing.Sequence[CreateAgentRequestToolRulesItem]] = OMIT,
        tags: typing.Optional[typing.Sequence[str]] = OMIT,
        system: typing.Optional[str] = OMIT,
        agent_type: typing.Optional[AgentType] = OMIT,
        llm_config: typing.Optional[LlmConfig] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        initial_message_sequence: typing.Optional[typing.Sequence[MessageCreate]] = OMIT,
        include_base_tools: typing.Optional[bool] = OMIT,
        include_multi_agent_tools: typing.Optional[bool] = OMIT,
        include_base_tool_rules: typing.Optional[bool] = OMIT,
        include_default_source: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        model: typing.Optional[str] = OMIT,
        embedding: typing.Optional[str] = OMIT,
        context_window_limit: typing.Optional[int] = OMIT,
        embedding_chunk_size: typing.Optional[int] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        max_reasoning_tokens: typing.Optional[int] = OMIT,
        enable_reasoner: typing.Optional[bool] = OMIT,
        reasoning: typing.Optional[bool] = OMIT,
        from_template: typing.Optional[str] = OMIT,
        template: typing.Optional[bool] = OMIT,
        project: typing.Optional[str] = OMIT,
        tool_exec_environment_variables: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        memory_variables: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        template_id: typing.Optional[str] = OMIT,
        base_template_id: typing.Optional[str] = OMIT,
        identity_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        message_buffer_autoclear: typing.Optional[bool] = OMIT,
        enable_sleeptime: typing.Optional[bool] = OMIT,
        response_format: typing.Optional[CreateAgentRequestResponseFormat] = OMIT,
        timezone: typing.Optional[str] = OMIT,
        max_files_open: typing.Optional[int] = OMIT,
        per_file_view_window_char_limit: typing.Optional[int] = OMIT,
        hidden: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AgentState:
        """
        Create a new agent with the specified configuration.

        Parameters
        ----------
        name : typing.Optional[str]
            The name of the agent.

        memory_blocks : typing.Optional[typing.Sequence[CreateBlock]]
            The blocks to create in the agent's in-context memory.

        tools : typing.Optional[typing.Sequence[str]]
            The tools used by the agent.

        tool_ids : typing.Optional[typing.Sequence[str]]
            The ids of the tools used by the agent.

        source_ids : typing.Optional[typing.Sequence[str]]
            The ids of the sources used by the agent.

        block_ids : typing.Optional[typing.Sequence[str]]
            The ids of the blocks used by the agent.

        tool_rules : typing.Optional[typing.Sequence[CreateAgentRequestToolRulesItem]]
            The tool rules governing the agent.

        tags : typing.Optional[typing.Sequence[str]]
            The tags associated with the agent.

        system : typing.Optional[str]
            The system prompt used by the agent.

        agent_type : typing.Optional[AgentType]
            The type of agent.

        llm_config : typing.Optional[LlmConfig]
            The LLM configuration used by the agent.

        embedding_config : typing.Optional[EmbeddingConfig]
            The embedding configuration used by the agent.

        initial_message_sequence : typing.Optional[typing.Sequence[MessageCreate]]
            The initial set of messages to put in the agent's in-context memory.

        include_base_tools : typing.Optional[bool]
            If true, attaches the Letta core tools (e.g. core_memory related functions).

        include_multi_agent_tools : typing.Optional[bool]
            If true, attaches the Letta multi-agent tools (e.g. sending a message to another agent).

        include_base_tool_rules : typing.Optional[bool]
            If true, attaches the Letta base tool rules (e.g. deny all tools not explicitly allowed).

        include_default_source : typing.Optional[bool]
            If true, automatically creates and attaches a default data source for this agent.

        description : typing.Optional[str]
            The description of the agent.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The metadata of the agent.

        model : typing.Optional[str]
            The LLM configuration handle used by the agent, specified in the format provider/model-name, as an alternative to specifying llm_config.

        embedding : typing.Optional[str]
            The embedding configuration handle used by the agent, specified in the format provider/model-name.

        context_window_limit : typing.Optional[int]
            The context window limit used by the agent.

        embedding_chunk_size : typing.Optional[int]
            The embedding chunk size used by the agent.

        max_tokens : typing.Optional[int]
            The maximum number of tokens to generate, including reasoning step. If not set, the model will use its default value.

        max_reasoning_tokens : typing.Optional[int]
            The maximum number of tokens to generate for reasoning step. If not set, the model will use its default value.

        enable_reasoner : typing.Optional[bool]
            Whether to enable internal extended thinking step for a reasoner model.

        reasoning : typing.Optional[bool]
            Whether to enable reasoning for this agent.

        from_template : typing.Optional[str]
            The template id used to configure the agent

        template : typing.Optional[bool]
            Whether the agent is a template

        project : typing.Optional[str]
            Deprecated: Project should now be passed via the X-Project header instead of in the request body. If using the sdk, this can be done via the new x_project field below.

        tool_exec_environment_variables : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            The environment variables for tool execution specific to this agent.

        memory_variables : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            The variables that should be set for the agent.

        project_id : typing.Optional[str]
            The id of the project the agent belongs to.

        template_id : typing.Optional[str]
            The id of the template the agent belongs to.

        base_template_id : typing.Optional[str]
            The base template id of the agent.

        identity_ids : typing.Optional[typing.Sequence[str]]
            The ids of the identities associated with this agent.

        message_buffer_autoclear : typing.Optional[bool]
            If set to True, the agent will not remember previous messages (though the agent will still retain state via core memory blocks and archival/recall memory). Not recommended unless you have an advanced use case.

        enable_sleeptime : typing.Optional[bool]
            If set to True, memory management will move to a background agent thread.

        response_format : typing.Optional[CreateAgentRequestResponseFormat]
            The response format for the agent.

        timezone : typing.Optional[str]
            The timezone of the agent (IANA format).

        max_files_open : typing.Optional[int]
            Maximum number of files that can be open at once for this agent. Setting this too high may exceed the context window, which will break the agent.

        per_file_view_window_char_limit : typing.Optional[int]
            The per-file view window character limit for this agent. Setting this too high may exceed the context window, which will break the agent.

        hidden : typing.Optional[bool]
            If set to True, the agent will be hidden.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.create()
        """
        _response = self._raw_client.create(
            name=name,
            memory_blocks=memory_blocks,
            tools=tools,
            tool_ids=tool_ids,
            source_ids=source_ids,
            block_ids=block_ids,
            tool_rules=tool_rules,
            tags=tags,
            system=system,
            agent_type=agent_type,
            llm_config=llm_config,
            embedding_config=embedding_config,
            initial_message_sequence=initial_message_sequence,
            include_base_tools=include_base_tools,
            include_multi_agent_tools=include_multi_agent_tools,
            include_base_tool_rules=include_base_tool_rules,
            include_default_source=include_default_source,
            description=description,
            metadata=metadata,
            model=model,
            embedding=embedding,
            context_window_limit=context_window_limit,
            embedding_chunk_size=embedding_chunk_size,
            max_tokens=max_tokens,
            max_reasoning_tokens=max_reasoning_tokens,
            enable_reasoner=enable_reasoner,
            reasoning=reasoning,
            from_template=from_template,
            template=template,
            project=project,
            tool_exec_environment_variables=tool_exec_environment_variables,
            memory_variables=memory_variables,
            project_id=project_id,
            template_id=template_id,
            base_template_id=base_template_id,
            identity_ids=identity_ids,
            message_buffer_autoclear=message_buffer_autoclear,
            enable_sleeptime=enable_sleeptime,
            response_format=response_format,
            timezone=timezone,
            max_files_open=max_files_open,
            per_file_view_window_char_limit=per_file_view_window_char_limit,
            hidden=hidden,
            request_options=request_options,
        )
        return _response.data

    def count(self, *, request_options: typing.Optional[RequestOptions] = None) -> int:
        """
        Get the count of all agents associated with a given user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        int
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.count()
        """
        _response = self._raw_client.count(request_options=request_options)
        return _response.data

    def export_file(
        self,
        agent_id: str,
        *,
        max_steps: typing.Optional[int] = None,
        use_legacy_format: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> str:
        """
        Export the serialized JSON representation of an agent, formatted with indentation.

        Supports two export formats:
        - Legacy format (use_legacy_format=true): Single agent with inline tools/blocks
        - New format (default): Multi-entity format with separate agents, tools, blocks, files, etc.

        Parameters
        ----------
        agent_id : str

        max_steps : typing.Optional[int]

        use_legacy_format : typing.Optional[bool]
            If true, exports using the legacy single-agent format. If false, exports using the new multi-entity format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        str
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.export_file(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.export_file(
            agent_id, max_steps=max_steps, use_legacy_format=use_legacy_format, request_options=request_options
        )
        return _response.data

    def import_file(
        self,
        *,
        file: core.File,
        append_copy_suffix: typing.Optional[bool] = OMIT,
        override_existing_tools: typing.Optional[bool] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        strip_messages: typing.Optional[bool] = OMIT,
        env_vars: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ImportedAgentsResponse:
        """
        Import a serialized agent file and recreate the agent(s) in the system.
        Returns the IDs of all imported agents.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        append_copy_suffix : typing.Optional[bool]
            If set to True, appends "_copy" to the end of the agent name.

        override_existing_tools : typing.Optional[bool]
            If set to True, existing tools can get their source code overwritten by the uploaded tool definitions. Note that Letta core tools can never be updated externally.

        project_id : typing.Optional[str]
            The project ID to associate the uploaded agent with.

        strip_messages : typing.Optional[bool]
            If set to True, strips all messages from the agent before importing.

        env_vars : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Environment variables to pass to the agent for tool execution.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ImportedAgentsResponse
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.import_file()
        """
        _response = self._raw_client.import_file(
            file=file,
            append_copy_suffix=append_copy_suffix,
            override_existing_tools=override_existing_tools,
            project_id=project_id,
            strip_messages=strip_messages,
            env_vars=env_vars,
            request_options=request_options,
        )
        return _response.data

    def retrieve(
        self,
        agent_id: str,
        *,
        include_relationships: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AgentState:
        """
        Get the state of the agent.

        Parameters
        ----------
        agent_id : str

        include_relationships : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            Specify which relational fields (e.g., 'tools', 'sources', 'memory') to include in the response. If not provided, all relationships are loaded by default. Using this can optimize performance by reducing unnecessary joins.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.retrieve(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.retrieve(
            agent_id, include_relationships=include_relationships, request_options=request_options
        )
        return _response.data

    def delete(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete an agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.delete(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.delete(agent_id, request_options=request_options)
        return _response.data

    def modify(
        self,
        agent_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        tool_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        source_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        block_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        tags: typing.Optional[typing.Sequence[str]] = OMIT,
        system: typing.Optional[str] = OMIT,
        tool_rules: typing.Optional[typing.Sequence[UpdateAgentToolRulesItem]] = OMIT,
        llm_config: typing.Optional[LlmConfig] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        message_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        tool_exec_environment_variables: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        template_id: typing.Optional[str] = OMIT,
        base_template_id: typing.Optional[str] = OMIT,
        identity_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        message_buffer_autoclear: typing.Optional[bool] = OMIT,
        model: typing.Optional[str] = OMIT,
        embedding: typing.Optional[str] = OMIT,
        reasoning: typing.Optional[bool] = OMIT,
        enable_sleeptime: typing.Optional[bool] = OMIT,
        response_format: typing.Optional[UpdateAgentResponseFormat] = OMIT,
        last_run_completion: typing.Optional[dt.datetime] = OMIT,
        last_run_duration_ms: typing.Optional[int] = OMIT,
        timezone: typing.Optional[str] = OMIT,
        max_files_open: typing.Optional[int] = OMIT,
        per_file_view_window_char_limit: typing.Optional[int] = OMIT,
        hidden: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AgentState:
        """
        Update an existing agent

        Parameters
        ----------
        agent_id : str

        name : typing.Optional[str]
            The name of the agent.

        tool_ids : typing.Optional[typing.Sequence[str]]
            The ids of the tools used by the agent.

        source_ids : typing.Optional[typing.Sequence[str]]
            The ids of the sources used by the agent.

        block_ids : typing.Optional[typing.Sequence[str]]
            The ids of the blocks used by the agent.

        tags : typing.Optional[typing.Sequence[str]]
            The tags associated with the agent.

        system : typing.Optional[str]
            The system prompt used by the agent.

        tool_rules : typing.Optional[typing.Sequence[UpdateAgentToolRulesItem]]
            The tool rules governing the agent.

        llm_config : typing.Optional[LlmConfig]
            The LLM configuration used by the agent.

        embedding_config : typing.Optional[EmbeddingConfig]
            The embedding configuration used by the agent.

        message_ids : typing.Optional[typing.Sequence[str]]
            The ids of the messages in the agent's in-context memory.

        description : typing.Optional[str]
            The description of the agent.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The metadata of the agent.

        tool_exec_environment_variables : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            The environment variables for tool execution specific to this agent.

        project_id : typing.Optional[str]
            The id of the project the agent belongs to.

        template_id : typing.Optional[str]
            The id of the template the agent belongs to.

        base_template_id : typing.Optional[str]
            The base template id of the agent.

        identity_ids : typing.Optional[typing.Sequence[str]]
            The ids of the identities associated with this agent.

        message_buffer_autoclear : typing.Optional[bool]
            If set to True, the agent will not remember previous messages (though the agent will still retain state via core memory blocks and archival/recall memory). Not recommended unless you have an advanced use case.

        model : typing.Optional[str]
            The LLM configuration handle used by the agent, specified in the format provider/model-name, as an alternative to specifying llm_config.

        embedding : typing.Optional[str]
            The embedding configuration handle used by the agent, specified in the format provider/model-name.

        reasoning : typing.Optional[bool]
            Whether to enable reasoning for this agent.

        enable_sleeptime : typing.Optional[bool]
            If set to True, memory management will move to a background agent thread.

        response_format : typing.Optional[UpdateAgentResponseFormat]
            The response format for the agent.

        last_run_completion : typing.Optional[dt.datetime]
            The timestamp when the agent last completed a run.

        last_run_duration_ms : typing.Optional[int]
            The duration in milliseconds of the agent's last run.

        timezone : typing.Optional[str]
            The timezone of the agent (IANA format).

        max_files_open : typing.Optional[int]
            Maximum number of files that can be open at once for this agent. Setting this too high may exceed the context window, which will break the agent.

        per_file_view_window_char_limit : typing.Optional[int]
            The per-file view window character limit for this agent. Setting this too high may exceed the context window, which will break the agent.

        hidden : typing.Optional[bool]
            If set to True, the agent will be hidden.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.modify(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.modify(
            agent_id,
            name=name,
            tool_ids=tool_ids,
            source_ids=source_ids,
            block_ids=block_ids,
            tags=tags,
            system=system,
            tool_rules=tool_rules,
            llm_config=llm_config,
            embedding_config=embedding_config,
            message_ids=message_ids,
            description=description,
            metadata=metadata,
            tool_exec_environment_variables=tool_exec_environment_variables,
            project_id=project_id,
            template_id=template_id,
            base_template_id=base_template_id,
            identity_ids=identity_ids,
            message_buffer_autoclear=message_buffer_autoclear,
            model=model,
            embedding=embedding,
            reasoning=reasoning,
            enable_sleeptime=enable_sleeptime,
            response_format=response_format,
            last_run_completion=last_run_completion,
            last_run_duration_ms=last_run_duration_ms,
            timezone=timezone,
            max_files_open=max_files_open,
            per_file_view_window_char_limit=per_file_view_window_char_limit,
            hidden=hidden,
            request_options=request_options,
        )
        return _response.data

    def list_agent_files(
        self,
        agent_id: str,
        *,
        cursor: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        is_open: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PaginatedAgentFiles:
        """
        Get the files attached to an agent with their open/closed status (paginated).

        Parameters
        ----------
        agent_id : str

        cursor : typing.Optional[str]
            Pagination cursor from previous response

        limit : typing.Optional[int]
            Number of items to return (1-100)

        is_open : typing.Optional[bool]
            Filter by open status (true for open files, false for closed files)

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PaginatedAgentFiles
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.list_agent_files(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.list_agent_files(
            agent_id, cursor=cursor, limit=limit, is_open=is_open, request_options=request_options
        )
        return _response.data

    def summarize_agent_conversation(
        self, agent_id: str, *, max_message_length: int, request_options: typing.Optional[RequestOptions] = None
    ) -> None:
        """
        Summarize an agent's conversation history to a target message length.

        This endpoint summarizes the current message history for a given agent,
        truncating and compressing it down to the specified `max_message_length`.

        Parameters
        ----------
        agent_id : str

        max_message_length : int
            Maximum number of messages to retain after summarization.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.summarize_agent_conversation(
            agent_id="agent_id",
            max_message_length=1,
        )
        """
        _response = self._raw_client.summarize_agent_conversation(
            agent_id, max_message_length=max_message_length, request_options=request_options
        )
        return _response.data

    def search(
        self,
        *,
        search: typing.Optional[typing.Sequence[AgentsSearchRequestSearchItem]] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        combinator: typing.Optional[typing.Literal["AND"]] = OMIT,
        limit: typing.Optional[float] = OMIT,
        after: typing.Optional[str] = OMIT,
        sort_by: typing.Optional[AgentsSearchRequestSortBy] = OMIT,
        ascending: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AgentsSearchResponse:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Search deployed agents.

        Parameters
        ----------
        search : typing.Optional[typing.Sequence[AgentsSearchRequestSearchItem]]

        project_id : typing.Optional[str]

        combinator : typing.Optional[typing.Literal["AND"]]

        limit : typing.Optional[float]

        after : typing.Optional[str]

        sort_by : typing.Optional[AgentsSearchRequestSortBy]

        ascending : typing.Optional[bool]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentsSearchResponse
            200

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.search()
        """
        _response = self._raw_client.search(
            search=search,
            project_id=project_id,
            combinator=combinator,
            limit=limit,
            after=after,
            sort_by=sort_by,
            ascending=ascending,
            request_options=request_options,
        )
        return _response.data


class AsyncAgentsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawAgentsClient(client_wrapper=client_wrapper)
        self.context = AsyncContextClient(client_wrapper=client_wrapper)

        self.tools = AsyncToolsClient(client_wrapper=client_wrapper)

        self.sources = AsyncSourcesClient(client_wrapper=client_wrapper)

        self.folders = AsyncFoldersClient(client_wrapper=client_wrapper)

        self.files = AsyncFilesClient(client_wrapper=client_wrapper)

        self.core_memory = AsyncCoreMemoryClient(client_wrapper=client_wrapper)

        self.blocks = AsyncBlocksClient(client_wrapper=client_wrapper)

        self.passages = AsyncPassagesClient(client_wrapper=client_wrapper)

        self.messages = AsyncMessagesClient(client_wrapper=client_wrapper)

        self.groups = AsyncGroupsClient(client_wrapper=client_wrapper)

        self.templates = AsyncTemplatesClient(client_wrapper=client_wrapper)

        self.memory_variables = AsyncMemoryVariablesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawAgentsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawAgentsClient
        """
        return self._raw_client

    async def list(
        self,
        *,
        name: typing.Optional[str] = None,
        tags: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        match_all_tags: typing.Optional[bool] = None,
        before: typing.Optional[str] = None,
        after: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        query_text: typing.Optional[str] = None,
        project_id: typing.Optional[str] = None,
        template_id: typing.Optional[str] = None,
        base_template_id: typing.Optional[str] = None,
        identity_id: typing.Optional[str] = None,
        identifier_keys: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        include_relationships: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        ascending: typing.Optional[bool] = None,
        sort_by: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[AgentState]:
        """
        List all agents associated with a given user.

        This endpoint retrieves a list of all agents and their configurations
        associated with the specified user ID.

        Parameters
        ----------
        name : typing.Optional[str]
            Name of the agent

        tags : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            List of tags to filter agents by

        match_all_tags : typing.Optional[bool]
            If True, only returns agents that match ALL given tags. Otherwise, return agents that have ANY of the passed-in tags.

        before : typing.Optional[str]
            Cursor for pagination

        after : typing.Optional[str]
            Cursor for pagination

        limit : typing.Optional[int]
            Limit for pagination

        query_text : typing.Optional[str]
            Search agents by name

        project_id : typing.Optional[str]
            Search agents by project ID - this will default to your default project on cloud

        template_id : typing.Optional[str]
            Search agents by template ID

        base_template_id : typing.Optional[str]
            Search agents by base template ID

        identity_id : typing.Optional[str]
            Search agents by identity ID

        identifier_keys : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            Search agents by identifier keys

        include_relationships : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            Specify which relational fields (e.g., 'tools', 'sources', 'memory') to include in the response. If not provided, all relationships are loaded by default. Using this can optimize performance by reducing unnecessary joins.

        ascending : typing.Optional[bool]
            Whether to sort agents oldest to newest (True) or newest to oldest (False, default)

        sort_by : typing.Optional[str]
            Field to sort by. Options: 'created_at' (default), 'last_run_completion'

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[AgentState]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            name=name,
            tags=tags,
            match_all_tags=match_all_tags,
            before=before,
            after=after,
            limit=limit,
            query_text=query_text,
            project_id=project_id,
            template_id=template_id,
            base_template_id=base_template_id,
            identity_id=identity_id,
            identifier_keys=identifier_keys,
            include_relationships=include_relationships,
            ascending=ascending,
            sort_by=sort_by,
            request_options=request_options,
        )
        return _response.data

    async def create(
        self,
        *,
        name: typing.Optional[str] = OMIT,
        memory_blocks: typing.Optional[typing.Sequence[CreateBlock]] = OMIT,
        tools: typing.Optional[typing.Sequence[str]] = OMIT,
        tool_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        source_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        block_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        tool_rules: typing.Optional[typing.Sequence[CreateAgentRequestToolRulesItem]] = OMIT,
        tags: typing.Optional[typing.Sequence[str]] = OMIT,
        system: typing.Optional[str] = OMIT,
        agent_type: typing.Optional[AgentType] = OMIT,
        llm_config: typing.Optional[LlmConfig] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        initial_message_sequence: typing.Optional[typing.Sequence[MessageCreate]] = OMIT,
        include_base_tools: typing.Optional[bool] = OMIT,
        include_multi_agent_tools: typing.Optional[bool] = OMIT,
        include_base_tool_rules: typing.Optional[bool] = OMIT,
        include_default_source: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        model: typing.Optional[str] = OMIT,
        embedding: typing.Optional[str] = OMIT,
        context_window_limit: typing.Optional[int] = OMIT,
        embedding_chunk_size: typing.Optional[int] = OMIT,
        max_tokens: typing.Optional[int] = OMIT,
        max_reasoning_tokens: typing.Optional[int] = OMIT,
        enable_reasoner: typing.Optional[bool] = OMIT,
        reasoning: typing.Optional[bool] = OMIT,
        from_template: typing.Optional[str] = OMIT,
        template: typing.Optional[bool] = OMIT,
        project: typing.Optional[str] = OMIT,
        tool_exec_environment_variables: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        memory_variables: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        template_id: typing.Optional[str] = OMIT,
        base_template_id: typing.Optional[str] = OMIT,
        identity_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        message_buffer_autoclear: typing.Optional[bool] = OMIT,
        enable_sleeptime: typing.Optional[bool] = OMIT,
        response_format: typing.Optional[CreateAgentRequestResponseFormat] = OMIT,
        timezone: typing.Optional[str] = OMIT,
        max_files_open: typing.Optional[int] = OMIT,
        per_file_view_window_char_limit: typing.Optional[int] = OMIT,
        hidden: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AgentState:
        """
        Create a new agent with the specified configuration.

        Parameters
        ----------
        name : typing.Optional[str]
            The name of the agent.

        memory_blocks : typing.Optional[typing.Sequence[CreateBlock]]
            The blocks to create in the agent's in-context memory.

        tools : typing.Optional[typing.Sequence[str]]
            The tools used by the agent.

        tool_ids : typing.Optional[typing.Sequence[str]]
            The ids of the tools used by the agent.

        source_ids : typing.Optional[typing.Sequence[str]]
            The ids of the sources used by the agent.

        block_ids : typing.Optional[typing.Sequence[str]]
            The ids of the blocks used by the agent.

        tool_rules : typing.Optional[typing.Sequence[CreateAgentRequestToolRulesItem]]
            The tool rules governing the agent.

        tags : typing.Optional[typing.Sequence[str]]
            The tags associated with the agent.

        system : typing.Optional[str]
            The system prompt used by the agent.

        agent_type : typing.Optional[AgentType]
            The type of agent.

        llm_config : typing.Optional[LlmConfig]
            The LLM configuration used by the agent.

        embedding_config : typing.Optional[EmbeddingConfig]
            The embedding configuration used by the agent.

        initial_message_sequence : typing.Optional[typing.Sequence[MessageCreate]]
            The initial set of messages to put in the agent's in-context memory.

        include_base_tools : typing.Optional[bool]
            If true, attaches the Letta core tools (e.g. core_memory related functions).

        include_multi_agent_tools : typing.Optional[bool]
            If true, attaches the Letta multi-agent tools (e.g. sending a message to another agent).

        include_base_tool_rules : typing.Optional[bool]
            If true, attaches the Letta base tool rules (e.g. deny all tools not explicitly allowed).

        include_default_source : typing.Optional[bool]
            If true, automatically creates and attaches a default data source for this agent.

        description : typing.Optional[str]
            The description of the agent.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The metadata of the agent.

        model : typing.Optional[str]
            The LLM configuration handle used by the agent, specified in the format provider/model-name, as an alternative to specifying llm_config.

        embedding : typing.Optional[str]
            The embedding configuration handle used by the agent, specified in the format provider/model-name.

        context_window_limit : typing.Optional[int]
            The context window limit used by the agent.

        embedding_chunk_size : typing.Optional[int]
            The embedding chunk size used by the agent.

        max_tokens : typing.Optional[int]
            The maximum number of tokens to generate, including reasoning step. If not set, the model will use its default value.

        max_reasoning_tokens : typing.Optional[int]
            The maximum number of tokens to generate for reasoning step. If not set, the model will use its default value.

        enable_reasoner : typing.Optional[bool]
            Whether to enable internal extended thinking step for a reasoner model.

        reasoning : typing.Optional[bool]
            Whether to enable reasoning for this agent.

        from_template : typing.Optional[str]
            The template id used to configure the agent

        template : typing.Optional[bool]
            Whether the agent is a template

        project : typing.Optional[str]
            Deprecated: Project should now be passed via the X-Project header instead of in the request body. If using the sdk, this can be done via the new x_project field below.

        tool_exec_environment_variables : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            The environment variables for tool execution specific to this agent.

        memory_variables : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            The variables that should be set for the agent.

        project_id : typing.Optional[str]
            The id of the project the agent belongs to.

        template_id : typing.Optional[str]
            The id of the template the agent belongs to.

        base_template_id : typing.Optional[str]
            The base template id of the agent.

        identity_ids : typing.Optional[typing.Sequence[str]]
            The ids of the identities associated with this agent.

        message_buffer_autoclear : typing.Optional[bool]
            If set to True, the agent will not remember previous messages (though the agent will still retain state via core memory blocks and archival/recall memory). Not recommended unless you have an advanced use case.

        enable_sleeptime : typing.Optional[bool]
            If set to True, memory management will move to a background agent thread.

        response_format : typing.Optional[CreateAgentRequestResponseFormat]
            The response format for the agent.

        timezone : typing.Optional[str]
            The timezone of the agent (IANA format).

        max_files_open : typing.Optional[int]
            Maximum number of files that can be open at once for this agent. Setting this too high may exceed the context window, which will break the agent.

        per_file_view_window_char_limit : typing.Optional[int]
            The per-file view window character limit for this agent. Setting this too high may exceed the context window, which will break the agent.

        hidden : typing.Optional[bool]
            If set to True, the agent will be hidden.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.create()


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            name=name,
            memory_blocks=memory_blocks,
            tools=tools,
            tool_ids=tool_ids,
            source_ids=source_ids,
            block_ids=block_ids,
            tool_rules=tool_rules,
            tags=tags,
            system=system,
            agent_type=agent_type,
            llm_config=llm_config,
            embedding_config=embedding_config,
            initial_message_sequence=initial_message_sequence,
            include_base_tools=include_base_tools,
            include_multi_agent_tools=include_multi_agent_tools,
            include_base_tool_rules=include_base_tool_rules,
            include_default_source=include_default_source,
            description=description,
            metadata=metadata,
            model=model,
            embedding=embedding,
            context_window_limit=context_window_limit,
            embedding_chunk_size=embedding_chunk_size,
            max_tokens=max_tokens,
            max_reasoning_tokens=max_reasoning_tokens,
            enable_reasoner=enable_reasoner,
            reasoning=reasoning,
            from_template=from_template,
            template=template,
            project=project,
            tool_exec_environment_variables=tool_exec_environment_variables,
            memory_variables=memory_variables,
            project_id=project_id,
            template_id=template_id,
            base_template_id=base_template_id,
            identity_ids=identity_ids,
            message_buffer_autoclear=message_buffer_autoclear,
            enable_sleeptime=enable_sleeptime,
            response_format=response_format,
            timezone=timezone,
            max_files_open=max_files_open,
            per_file_view_window_char_limit=per_file_view_window_char_limit,
            hidden=hidden,
            request_options=request_options,
        )
        return _response.data

    async def count(self, *, request_options: typing.Optional[RequestOptions] = None) -> int:
        """
        Get the count of all agents associated with a given user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        int
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.count()


        asyncio.run(main())
        """
        _response = await self._raw_client.count(request_options=request_options)
        return _response.data

    async def export_file(
        self,
        agent_id: str,
        *,
        max_steps: typing.Optional[int] = None,
        use_legacy_format: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> str:
        """
        Export the serialized JSON representation of an agent, formatted with indentation.

        Supports two export formats:
        - Legacy format (use_legacy_format=true): Single agent with inline tools/blocks
        - New format (default): Multi-entity format with separate agents, tools, blocks, files, etc.

        Parameters
        ----------
        agent_id : str

        max_steps : typing.Optional[int]

        use_legacy_format : typing.Optional[bool]
            If true, exports using the legacy single-agent format. If false, exports using the new multi-entity format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        str
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.export_file(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.export_file(
            agent_id, max_steps=max_steps, use_legacy_format=use_legacy_format, request_options=request_options
        )
        return _response.data

    async def import_file(
        self,
        *,
        file: core.File,
        append_copy_suffix: typing.Optional[bool] = OMIT,
        override_existing_tools: typing.Optional[bool] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        strip_messages: typing.Optional[bool] = OMIT,
        env_vars: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ImportedAgentsResponse:
        """
        Import a serialized agent file and recreate the agent(s) in the system.
        Returns the IDs of all imported agents.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        append_copy_suffix : typing.Optional[bool]
            If set to True, appends "_copy" to the end of the agent name.

        override_existing_tools : typing.Optional[bool]
            If set to True, existing tools can get their source code overwritten by the uploaded tool definitions. Note that Letta core tools can never be updated externally.

        project_id : typing.Optional[str]
            The project ID to associate the uploaded agent with.

        strip_messages : typing.Optional[bool]
            If set to True, strips all messages from the agent before importing.

        env_vars : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Environment variables to pass to the agent for tool execution.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ImportedAgentsResponse
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.import_file()


        asyncio.run(main())
        """
        _response = await self._raw_client.import_file(
            file=file,
            append_copy_suffix=append_copy_suffix,
            override_existing_tools=override_existing_tools,
            project_id=project_id,
            strip_messages=strip_messages,
            env_vars=env_vars,
            request_options=request_options,
        )
        return _response.data

    async def retrieve(
        self,
        agent_id: str,
        *,
        include_relationships: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AgentState:
        """
        Get the state of the agent.

        Parameters
        ----------
        agent_id : str

        include_relationships : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            Specify which relational fields (e.g., 'tools', 'sources', 'memory') to include in the response. If not provided, all relationships are loaded by default. Using this can optimize performance by reducing unnecessary joins.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.retrieve(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.retrieve(
            agent_id, include_relationships=include_relationships, request_options=request_options
        )
        return _response.data

    async def delete(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete an agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.delete(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(agent_id, request_options=request_options)
        return _response.data

    async def modify(
        self,
        agent_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        tool_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        source_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        block_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        tags: typing.Optional[typing.Sequence[str]] = OMIT,
        system: typing.Optional[str] = OMIT,
        tool_rules: typing.Optional[typing.Sequence[UpdateAgentToolRulesItem]] = OMIT,
        llm_config: typing.Optional[LlmConfig] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        message_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        tool_exec_environment_variables: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        template_id: typing.Optional[str] = OMIT,
        base_template_id: typing.Optional[str] = OMIT,
        identity_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        message_buffer_autoclear: typing.Optional[bool] = OMIT,
        model: typing.Optional[str] = OMIT,
        embedding: typing.Optional[str] = OMIT,
        reasoning: typing.Optional[bool] = OMIT,
        enable_sleeptime: typing.Optional[bool] = OMIT,
        response_format: typing.Optional[UpdateAgentResponseFormat] = OMIT,
        last_run_completion: typing.Optional[dt.datetime] = OMIT,
        last_run_duration_ms: typing.Optional[int] = OMIT,
        timezone: typing.Optional[str] = OMIT,
        max_files_open: typing.Optional[int] = OMIT,
        per_file_view_window_char_limit: typing.Optional[int] = OMIT,
        hidden: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AgentState:
        """
        Update an existing agent

        Parameters
        ----------
        agent_id : str

        name : typing.Optional[str]
            The name of the agent.

        tool_ids : typing.Optional[typing.Sequence[str]]
            The ids of the tools used by the agent.

        source_ids : typing.Optional[typing.Sequence[str]]
            The ids of the sources used by the agent.

        block_ids : typing.Optional[typing.Sequence[str]]
            The ids of the blocks used by the agent.

        tags : typing.Optional[typing.Sequence[str]]
            The tags associated with the agent.

        system : typing.Optional[str]
            The system prompt used by the agent.

        tool_rules : typing.Optional[typing.Sequence[UpdateAgentToolRulesItem]]
            The tool rules governing the agent.

        llm_config : typing.Optional[LlmConfig]
            The LLM configuration used by the agent.

        embedding_config : typing.Optional[EmbeddingConfig]
            The embedding configuration used by the agent.

        message_ids : typing.Optional[typing.Sequence[str]]
            The ids of the messages in the agent's in-context memory.

        description : typing.Optional[str]
            The description of the agent.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The metadata of the agent.

        tool_exec_environment_variables : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            The environment variables for tool execution specific to this agent.

        project_id : typing.Optional[str]
            The id of the project the agent belongs to.

        template_id : typing.Optional[str]
            The id of the template the agent belongs to.

        base_template_id : typing.Optional[str]
            The base template id of the agent.

        identity_ids : typing.Optional[typing.Sequence[str]]
            The ids of the identities associated with this agent.

        message_buffer_autoclear : typing.Optional[bool]
            If set to True, the agent will not remember previous messages (though the agent will still retain state via core memory blocks and archival/recall memory). Not recommended unless you have an advanced use case.

        model : typing.Optional[str]
            The LLM configuration handle used by the agent, specified in the format provider/model-name, as an alternative to specifying llm_config.

        embedding : typing.Optional[str]
            The embedding configuration handle used by the agent, specified in the format provider/model-name.

        reasoning : typing.Optional[bool]
            Whether to enable reasoning for this agent.

        enable_sleeptime : typing.Optional[bool]
            If set to True, memory management will move to a background agent thread.

        response_format : typing.Optional[UpdateAgentResponseFormat]
            The response format for the agent.

        last_run_completion : typing.Optional[dt.datetime]
            The timestamp when the agent last completed a run.

        last_run_duration_ms : typing.Optional[int]
            The duration in milliseconds of the agent's last run.

        timezone : typing.Optional[str]
            The timezone of the agent (IANA format).

        max_files_open : typing.Optional[int]
            Maximum number of files that can be open at once for this agent. Setting this too high may exceed the context window, which will break the agent.

        per_file_view_window_char_limit : typing.Optional[int]
            The per-file view window character limit for this agent. Setting this too high may exceed the context window, which will break the agent.

        hidden : typing.Optional[bool]
            If set to True, the agent will be hidden.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.modify(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.modify(
            agent_id,
            name=name,
            tool_ids=tool_ids,
            source_ids=source_ids,
            block_ids=block_ids,
            tags=tags,
            system=system,
            tool_rules=tool_rules,
            llm_config=llm_config,
            embedding_config=embedding_config,
            message_ids=message_ids,
            description=description,
            metadata=metadata,
            tool_exec_environment_variables=tool_exec_environment_variables,
            project_id=project_id,
            template_id=template_id,
            base_template_id=base_template_id,
            identity_ids=identity_ids,
            message_buffer_autoclear=message_buffer_autoclear,
            model=model,
            embedding=embedding,
            reasoning=reasoning,
            enable_sleeptime=enable_sleeptime,
            response_format=response_format,
            last_run_completion=last_run_completion,
            last_run_duration_ms=last_run_duration_ms,
            timezone=timezone,
            max_files_open=max_files_open,
            per_file_view_window_char_limit=per_file_view_window_char_limit,
            hidden=hidden,
            request_options=request_options,
        )
        return _response.data

    async def list_agent_files(
        self,
        agent_id: str,
        *,
        cursor: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        is_open: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PaginatedAgentFiles:
        """
        Get the files attached to an agent with their open/closed status (paginated).

        Parameters
        ----------
        agent_id : str

        cursor : typing.Optional[str]
            Pagination cursor from previous response

        limit : typing.Optional[int]
            Number of items to return (1-100)

        is_open : typing.Optional[bool]
            Filter by open status (true for open files, false for closed files)

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PaginatedAgentFiles
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.list_agent_files(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list_agent_files(
            agent_id, cursor=cursor, limit=limit, is_open=is_open, request_options=request_options
        )
        return _response.data

    async def summarize_agent_conversation(
        self, agent_id: str, *, max_message_length: int, request_options: typing.Optional[RequestOptions] = None
    ) -> None:
        """
        Summarize an agent's conversation history to a target message length.

        This endpoint summarizes the current message history for a given agent,
        truncating and compressing it down to the specified `max_message_length`.

        Parameters
        ----------
        agent_id : str

        max_message_length : int
            Maximum number of messages to retain after summarization.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.summarize_agent_conversation(
                agent_id="agent_id",
                max_message_length=1,
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.summarize_agent_conversation(
            agent_id, max_message_length=max_message_length, request_options=request_options
        )
        return _response.data

    async def search(
        self,
        *,
        search: typing.Optional[typing.Sequence[AgentsSearchRequestSearchItem]] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        combinator: typing.Optional[typing.Literal["AND"]] = OMIT,
        limit: typing.Optional[float] = OMIT,
        after: typing.Optional[str] = OMIT,
        sort_by: typing.Optional[AgentsSearchRequestSortBy] = OMIT,
        ascending: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AgentsSearchResponse:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Search deployed agents.

        Parameters
        ----------
        search : typing.Optional[typing.Sequence[AgentsSearchRequestSearchItem]]

        project_id : typing.Optional[str]

        combinator : typing.Optional[typing.Literal["AND"]]

        limit : typing.Optional[float]

        after : typing.Optional[str]

        sort_by : typing.Optional[AgentsSearchRequestSortBy]

        ascending : typing.Optional[bool]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentsSearchResponse
            200

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.search()


        asyncio.run(main())
        """
        _response = await self._raw_client.search(
            search=search,
            project_id=project_id,
            combinator=combinator,
            limit=limit,
            after=after,
            sort_by=sort_by,
            ascending=ascending,
            request_options=request_options,
        )
        return _response.data
