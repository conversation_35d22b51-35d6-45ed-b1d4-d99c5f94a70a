# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from .raw_client import AsyncRawTemplatesClient, RawTemplatesClient
from .types.templates_migrate_response import TemplatesMigrateResponse

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class TemplatesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawTemplatesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawTemplatesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawTemplatesClient
        """
        return self._raw_client

    def migrate(
        self,
        agent_id: str,
        *,
        to_template: str,
        preserve_core_memories: bool,
        preserve_tool_variables: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> TemplatesMigrateResponse:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Migrate an agent to a new versioned agent template.

        Parameters
        ----------
        agent_id : str

        to_template : str

        preserve_core_memories : bool

        preserve_tool_variables : typing.Optional[bool]
            If true, preserves the existing agent's tool environment variables instead of using the template's variables

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        TemplatesMigrateResponse
            200

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.templates.migrate(
            agent_id="agent_id",
            to_template="to_template",
            preserve_core_memories=True,
        )
        """
        _response = self._raw_client.migrate(
            agent_id,
            to_template=to_template,
            preserve_core_memories=preserve_core_memories,
            preserve_tool_variables=preserve_tool_variables,
            request_options=request_options,
        )
        return _response.data

    def create(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Creates a template from an agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.templates.create(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.create(agent_id, request_options=request_options)
        return _response.data

    def create_version(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Creates a new version of the template version of the agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.templates.create_version(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.create_version(agent_id, request_options=request_options)
        return _response.data


class AsyncTemplatesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawTemplatesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawTemplatesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawTemplatesClient
        """
        return self._raw_client

    async def migrate(
        self,
        agent_id: str,
        *,
        to_template: str,
        preserve_core_memories: bool,
        preserve_tool_variables: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> TemplatesMigrateResponse:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Migrate an agent to a new versioned agent template.

        Parameters
        ----------
        agent_id : str

        to_template : str

        preserve_core_memories : bool

        preserve_tool_variables : typing.Optional[bool]
            If true, preserves the existing agent's tool environment variables instead of using the template's variables

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        TemplatesMigrateResponse
            200

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.templates.migrate(
                agent_id="agent_id",
                to_template="to_template",
                preserve_core_memories=True,
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.migrate(
            agent_id,
            to_template=to_template,
            preserve_core_memories=preserve_core_memories,
            preserve_tool_variables=preserve_tool_variables,
            request_options=request_options,
        )
        return _response.data

    async def create(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Creates a template from an agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.templates.create(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(agent_id, request_options=request_options)
        return _response.data

    async def create_version(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Creates a new version of the template version of the agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.templates.create_version(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create_version(agent_id, request_options=request_options)
        return _response.data
