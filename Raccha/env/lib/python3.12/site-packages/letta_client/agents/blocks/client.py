# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, Sync<PERSON><PERSON>Wrapper
from ...core.request_options import RequestOptions
from ...types.agent_state import AgentState
from ...types.block import Block
from .raw_client import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lient, RawBlocksClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class BlocksClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawBlocksClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawBlocksClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawBlocksClient
        """
        return self._raw_client

    def retrieve(
        self, agent_id: str, block_label: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> Block:
        """
        Retrieve a core memory block from an agent.

        Parameters
        ----------
        agent_id : str

        block_label : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.blocks.retrieve(
            agent_id="agent_id",
            block_label="block_label",
        )
        """
        _response = self._raw_client.retrieve(agent_id, block_label, request_options=request_options)
        return _response.data

    def modify(
        self,
        agent_id: str,
        block_label: str,
        *,
        value: typing.Optional[str] = OMIT,
        limit: typing.Optional[int] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        name: typing.Optional[str] = OMIT,
        is_template: typing.Optional[bool] = OMIT,
        preserve_on_migration: typing.Optional[bool] = OMIT,
        label: typing.Optional[str] = OMIT,
        read_only: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Block:
        """
        Updates a core memory block of an agent.

        Parameters
        ----------
        agent_id : str

        block_label : str

        value : typing.Optional[str]
            Value of the block.

        limit : typing.Optional[int]
            Character limit of the block.

        project_id : typing.Optional[str]
            The associated project id.

        name : typing.Optional[str]
            Name of the block if it is a template.

        is_template : typing.Optional[bool]
            Whether the block is a template (e.g. saved human/persona options).

        preserve_on_migration : typing.Optional[bool]
            Preserve the block on template migration.

        label : typing.Optional[str]
            Label of the block (e.g. 'human', 'persona') in the context window.

        read_only : typing.Optional[bool]
            Whether the agent has read-only access to the block.

        description : typing.Optional[str]
            Description of the block.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata of the block.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.blocks.modify(
            agent_id="agent_id",
            block_label="block_label",
        )
        """
        _response = self._raw_client.modify(
            agent_id,
            block_label,
            value=value,
            limit=limit,
            project_id=project_id,
            name=name,
            is_template=is_template,
            preserve_on_migration=preserve_on_migration,
            label=label,
            read_only=read_only,
            description=description,
            metadata=metadata,
            request_options=request_options,
        )
        return _response.data

    def list(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> typing.List[Block]:
        """
        Retrieve the core memory blocks of a specific agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Block]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.blocks.list(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.list(agent_id, request_options=request_options)
        return _response.data

    def attach(
        self, agent_id: str, block_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AgentState:
        """
        Attach a core memory block to an agent.

        Parameters
        ----------
        agent_id : str

        block_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.blocks.attach(
            agent_id="agent_id",
            block_id="block_id",
        )
        """
        _response = self._raw_client.attach(agent_id, block_id, request_options=request_options)
        return _response.data

    def detach(
        self, agent_id: str, block_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AgentState:
        """
        Detach a core memory block from an agent.

        Parameters
        ----------
        agent_id : str

        block_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.blocks.detach(
            agent_id="agent_id",
            block_id="block_id",
        )
        """
        _response = self._raw_client.detach(agent_id, block_id, request_options=request_options)
        return _response.data


class AsyncBlocksClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawBlocksClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawBlocksClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawBlocksClient
        """
        return self._raw_client

    async def retrieve(
        self, agent_id: str, block_label: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> Block:
        """
        Retrieve a core memory block from an agent.

        Parameters
        ----------
        agent_id : str

        block_label : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.blocks.retrieve(
                agent_id="agent_id",
                block_label="block_label",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.retrieve(agent_id, block_label, request_options=request_options)
        return _response.data

    async def modify(
        self,
        agent_id: str,
        block_label: str,
        *,
        value: typing.Optional[str] = OMIT,
        limit: typing.Optional[int] = OMIT,
        project_id: typing.Optional[str] = OMIT,
        name: typing.Optional[str] = OMIT,
        is_template: typing.Optional[bool] = OMIT,
        preserve_on_migration: typing.Optional[bool] = OMIT,
        label: typing.Optional[str] = OMIT,
        read_only: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Block:
        """
        Updates a core memory block of an agent.

        Parameters
        ----------
        agent_id : str

        block_label : str

        value : typing.Optional[str]
            Value of the block.

        limit : typing.Optional[int]
            Character limit of the block.

        project_id : typing.Optional[str]
            The associated project id.

        name : typing.Optional[str]
            Name of the block if it is a template.

        is_template : typing.Optional[bool]
            Whether the block is a template (e.g. saved human/persona options).

        preserve_on_migration : typing.Optional[bool]
            Preserve the block on template migration.

        label : typing.Optional[str]
            Label of the block (e.g. 'human', 'persona') in the context window.

        read_only : typing.Optional[bool]
            Whether the agent has read-only access to the block.

        description : typing.Optional[str]
            Description of the block.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata of the block.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Block
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.blocks.modify(
                agent_id="agent_id",
                block_label="block_label",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.modify(
            agent_id,
            block_label,
            value=value,
            limit=limit,
            project_id=project_id,
            name=name,
            is_template=is_template,
            preserve_on_migration=preserve_on_migration,
            label=label,
            read_only=read_only,
            description=description,
            metadata=metadata,
            request_options=request_options,
        )
        return _response.data

    async def list(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[Block]:
        """
        Retrieve the core memory blocks of a specific agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Block]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.blocks.list(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(agent_id, request_options=request_options)
        return _response.data

    async def attach(
        self, agent_id: str, block_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AgentState:
        """
        Attach a core memory block to an agent.

        Parameters
        ----------
        agent_id : str

        block_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.blocks.attach(
                agent_id="agent_id",
                block_id="block_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.attach(agent_id, block_id, request_options=request_options)
        return _response.data

    async def detach(
        self, agent_id: str, block_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AgentState:
        """
        Detach a core memory block from an agent.

        Parameters
        ----------
        agent_id : str

        block_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AgentState
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.blocks.detach(
                agent_id="agent_id",
                block_id="block_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.detach(agent_id, block_id, request_options=request_options)
        return _response.data
