# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, Sync<PERSON>lientWrapper
from ...core.request_options import RequestOptions
from ...types.memory import Memory
from .raw_client import AsyncRawCoreMemoryClient, RawCoreMemoryClient


class CoreMemoryClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawCoreMemoryClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawCoreMemoryClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawCoreMemoryClient
        """
        return self._raw_client

    def retrieve(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> Memory:
        """
        Retrieve the memory state of a specific agent.
        This endpoint fetches the current memory state of the agent identified by the user ID and agent ID.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Memory
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.core_memory.retrieve(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.retrieve(agent_id, request_options=request_options)
        return _response.data


class AsyncCoreMemoryClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawCoreMemoryClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawCoreMemoryClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawCoreMemoryClient
        """
        return self._raw_client

    async def retrieve(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> Memory:
        """
        Retrieve the memory state of a specific agent.
        This endpoint fetches the current memory state of the agent identified by the user ID and agent ID.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Memory
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.core_memory.retrieve(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.retrieve(agent_id, request_options=request_options)
        return _response.data
