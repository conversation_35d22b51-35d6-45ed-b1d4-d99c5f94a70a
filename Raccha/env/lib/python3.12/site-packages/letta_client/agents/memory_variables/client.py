# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from .raw_client import AsyncRawMemoryVariablesClient, RawMemoryVariablesClient
from .types.memory_variables_list_response import MemoryVariablesListResponse


class MemoryVariablesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawMemoryVariablesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawMemoryVariablesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawMemoryVariablesClient
        """
        return self._raw_client

    def list(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> MemoryVariablesListResponse:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Returns the memory variables associated with an agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        MemoryVariablesListResponse
            200

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.memory_variables.list(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.list(agent_id, request_options=request_options)
        return _response.data


class AsyncMemoryVariablesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawMemoryVariablesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawMemoryVariablesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawMemoryVariablesClient
        """
        return self._raw_client

    async def list(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> MemoryVariablesListResponse:
        """
        <Note>This endpoint is only available on Letta Cloud.</Note>

        Returns the memory variables associated with an agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        MemoryVariablesListResponse
            200

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.memory_variables.list(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(agent_id, request_options=request_options)
        return _response.data
