# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.embedding_config import EmbeddingConfig
from ...types.passage import Passage
from .raw_client import AsyncRaw<PERSON><PERSON>agesClient, RawPassagesClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class PassagesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawPassagesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawPassagesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawPassagesClient
        """
        return self._raw_client

    def list(
        self,
        agent_id: str,
        *,
        after: typing.Optional[str] = None,
        before: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        ascending: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Passage]:
        """
        Retrieve the memories in an agent's archival memory store (paginated query).

        Parameters
        ----------
        agent_id : str

        after : typing.Optional[str]
            Unique ID of the memory to start the query range at.

        before : typing.Optional[str]
            Unique ID of the memory to end the query range at.

        limit : typing.Optional[int]
            How many results to include in the response.

        search : typing.Optional[str]
            Search passages by text

        ascending : typing.Optional[bool]
            Whether to sort passages oldest to newest (True, default) or newest to oldest (False)

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Passage]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.passages.list(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.list(
            agent_id,
            after=after,
            before=before,
            limit=limit,
            search=search,
            ascending=ascending,
            request_options=request_options,
        )
        return _response.data

    def create(
        self, agent_id: str, *, text: str, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[Passage]:
        """
        Insert a memory into an agent's archival memory store.

        Parameters
        ----------
        agent_id : str

        text : str
            Text to write to archival memory.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Passage]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.passages.create(
            agent_id="agent_id",
            text="text",
        )
        """
        _response = self._raw_client.create(agent_id, text=text, request_options=request_options)
        return _response.data

    def delete(
        self, agent_id: str, memory_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a memory from an agent's archival memory store.

        Parameters
        ----------
        agent_id : str

        memory_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.passages.delete(
            agent_id="agent_id",
            memory_id="memory_id",
        )
        """
        _response = self._raw_client.delete(agent_id, memory_id, request_options=request_options)
        return _response.data

    def modify(
        self,
        agent_id: str,
        memory_id: str,
        *,
        id: str,
        created_by_id: typing.Optional[str] = OMIT,
        last_updated_by_id: typing.Optional[str] = OMIT,
        created_at: typing.Optional[dt.datetime] = OMIT,
        updated_at: typing.Optional[dt.datetime] = OMIT,
        is_deleted: typing.Optional[bool] = OMIT,
        archive_id: typing.Optional[str] = OMIT,
        source_id: typing.Optional[str] = OMIT,
        file_id: typing.Optional[str] = OMIT,
        file_name: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        text: typing.Optional[str] = OMIT,
        embedding: typing.Optional[typing.Sequence[float]] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Passage]:
        """
        Modify a memory in the agent's archival memory store.

        Parameters
        ----------
        agent_id : str

        memory_id : str

        id : str
            The unique identifier of the passage.

        created_by_id : typing.Optional[str]
            The id of the user that made this object.

        last_updated_by_id : typing.Optional[str]
            The id of the user that made this object.

        created_at : typing.Optional[dt.datetime]
            The timestamp when the object was created.

        updated_at : typing.Optional[dt.datetime]
            The timestamp when the object was last updated.

        is_deleted : typing.Optional[bool]
            Whether this passage is deleted or not.

        archive_id : typing.Optional[str]
            The unique identifier of the archive containing this passage.

        source_id : typing.Optional[str]
            The data source of the passage.

        file_id : typing.Optional[str]
            The unique identifier of the file associated with the passage.

        file_name : typing.Optional[str]
            The name of the file (only for source passages).

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The metadata of the passage.

        text : typing.Optional[str]
            The text of the passage.

        embedding : typing.Optional[typing.Sequence[float]]
            The embedding of the passage.

        embedding_config : typing.Optional[EmbeddingConfig]
            The embedding configuration used by the passage.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Passage]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.passages.modify(
            agent_id="agent_id",
            memory_id="memory_id",
            id="id",
        )
        """
        _response = self._raw_client.modify(
            agent_id,
            memory_id,
            id=id,
            created_by_id=created_by_id,
            last_updated_by_id=last_updated_by_id,
            created_at=created_at,
            updated_at=updated_at,
            is_deleted=is_deleted,
            archive_id=archive_id,
            source_id=source_id,
            file_id=file_id,
            file_name=file_name,
            metadata=metadata,
            text=text,
            embedding=embedding,
            embedding_config=embedding_config,
            request_options=request_options,
        )
        return _response.data


class AsyncPassagesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawPassagesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawPassagesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawPassagesClient
        """
        return self._raw_client

    async def list(
        self,
        agent_id: str,
        *,
        after: typing.Optional[str] = None,
        before: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        ascending: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Passage]:
        """
        Retrieve the memories in an agent's archival memory store (paginated query).

        Parameters
        ----------
        agent_id : str

        after : typing.Optional[str]
            Unique ID of the memory to start the query range at.

        before : typing.Optional[str]
            Unique ID of the memory to end the query range at.

        limit : typing.Optional[int]
            How many results to include in the response.

        search : typing.Optional[str]
            Search passages by text

        ascending : typing.Optional[bool]
            Whether to sort passages oldest to newest (True, default) or newest to oldest (False)

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Passage]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.passages.list(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            agent_id,
            after=after,
            before=before,
            limit=limit,
            search=search,
            ascending=ascending,
            request_options=request_options,
        )
        return _response.data

    async def create(
        self, agent_id: str, *, text: str, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[Passage]:
        """
        Insert a memory into an agent's archival memory store.

        Parameters
        ----------
        agent_id : str

        text : str
            Text to write to archival memory.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Passage]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.passages.create(
                agent_id="agent_id",
                text="text",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(agent_id, text=text, request_options=request_options)
        return _response.data

    async def delete(
        self, agent_id: str, memory_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a memory from an agent's archival memory store.

        Parameters
        ----------
        agent_id : str

        memory_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.passages.delete(
                agent_id="agent_id",
                memory_id="memory_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(agent_id, memory_id, request_options=request_options)
        return _response.data

    async def modify(
        self,
        agent_id: str,
        memory_id: str,
        *,
        id: str,
        created_by_id: typing.Optional[str] = OMIT,
        last_updated_by_id: typing.Optional[str] = OMIT,
        created_at: typing.Optional[dt.datetime] = OMIT,
        updated_at: typing.Optional[dt.datetime] = OMIT,
        is_deleted: typing.Optional[bool] = OMIT,
        archive_id: typing.Optional[str] = OMIT,
        source_id: typing.Optional[str] = OMIT,
        file_id: typing.Optional[str] = OMIT,
        file_name: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        text: typing.Optional[str] = OMIT,
        embedding: typing.Optional[typing.Sequence[float]] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Passage]:
        """
        Modify a memory in the agent's archival memory store.

        Parameters
        ----------
        agent_id : str

        memory_id : str

        id : str
            The unique identifier of the passage.

        created_by_id : typing.Optional[str]
            The id of the user that made this object.

        last_updated_by_id : typing.Optional[str]
            The id of the user that made this object.

        created_at : typing.Optional[dt.datetime]
            The timestamp when the object was created.

        updated_at : typing.Optional[dt.datetime]
            The timestamp when the object was last updated.

        is_deleted : typing.Optional[bool]
            Whether this passage is deleted or not.

        archive_id : typing.Optional[str]
            The unique identifier of the archive containing this passage.

        source_id : typing.Optional[str]
            The data source of the passage.

        file_id : typing.Optional[str]
            The unique identifier of the file associated with the passage.

        file_name : typing.Optional[str]
            The name of the file (only for source passages).

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The metadata of the passage.

        text : typing.Optional[str]
            The text of the passage.

        embedding : typing.Optional[typing.Sequence[float]]
            The embedding of the passage.

        embedding_config : typing.Optional[EmbeddingConfig]
            The embedding configuration used by the passage.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Passage]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.passages.modify(
                agent_id="agent_id",
                memory_id="memory_id",
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.modify(
            agent_id,
            memory_id,
            id=id,
            created_by_id=created_by_id,
            last_updated_by_id=last_updated_by_id,
            created_at=created_at,
            updated_at=updated_at,
            is_deleted=is_deleted,
            archive_id=archive_id,
            source_id=source_id,
            file_id=file_id,
            file_name=file_name,
            metadata=metadata,
            text=text,
            embedding=embedding,
            embedding_config=embedding_config,
            request_options=request_options,
        )
        return _response.data
