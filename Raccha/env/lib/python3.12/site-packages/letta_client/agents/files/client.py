# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from .raw_client import AsyncRaw<PERSON><PERSON><PERSON>lient, RawFilesClient


class FilesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawFilesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawFilesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawFilesClient
        """
        return self._raw_client

    def close_all(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> typing.List[str]:
        """
        Closes all currently open files for a given agent.

        This endpoint updates the file state for the agent so that no files are marked as open.
        Typically used to reset the working memory view for the agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[str]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.files.close_all(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.close_all(agent_id, request_options=request_options)
        return _response.data

    def open(
        self, agent_id: str, file_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[str]:
        """
        Opens a specific file for a given agent.

        This endpoint marks a specific file as open in the agent's file state.
        The file will be included in the agent's working memory view.
        Returns a list of file names that were closed due to LRU eviction.

        Parameters
        ----------
        agent_id : str

        file_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[str]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.files.open(
            agent_id="agent_id",
            file_id="file_id",
        )
        """
        _response = self._raw_client.open(agent_id, file_id, request_options=request_options)
        return _response.data

    def close(
        self, agent_id: str, file_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Closes a specific file for a given agent.

        This endpoint marks a specific file as closed in the agent's file state.
        The file will be removed from the agent's working memory view.

        Parameters
        ----------
        agent_id : str

        file_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.files.close(
            agent_id="agent_id",
            file_id="file_id",
        )
        """
        _response = self._raw_client.close(agent_id, file_id, request_options=request_options)
        return _response.data

    def list(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.files.list(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.list(agent_id, request_options=request_options)
        return _response.data


class AsyncFilesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawFilesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawFilesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawFilesClient
        """
        return self._raw_client

    async def close_all(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[str]:
        """
        Closes all currently open files for a given agent.

        This endpoint updates the file state for the agent so that no files are marked as open.
        Typically used to reset the working memory view for the agent.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[str]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.files.close_all(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.close_all(agent_id, request_options=request_options)
        return _response.data

    async def open(
        self, agent_id: str, file_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[str]:
        """
        Opens a specific file for a given agent.

        This endpoint marks a specific file as open in the agent's file state.
        The file will be included in the agent's working memory view.
        Returns a list of file names that were closed due to LRU eviction.

        Parameters
        ----------
        agent_id : str

        file_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[str]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.files.open(
                agent_id="agent_id",
                file_id="file_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.open(agent_id, file_id, request_options=request_options)
        return _response.data

    async def close(
        self, agent_id: str, file_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Closes a specific file for a given agent.

        This endpoint marks a specific file as closed in the agent's file state.
        The file will be removed from the agent's working memory view.

        Parameters
        ----------
        agent_id : str

        file_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.files.close(
                agent_id="agent_id",
                file_id="file_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.close(agent_id, file_id, request_options=request_options)
        return _response.data

    async def list(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.files.list(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(agent_id, request_options=request_options)
        return _response.data
