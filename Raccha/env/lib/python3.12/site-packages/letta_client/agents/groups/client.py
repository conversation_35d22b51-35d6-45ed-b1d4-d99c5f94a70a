# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.group import Group
from .raw_client import AsyncRawGroupsClient, RawGroupsClient


class GroupsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawGroupsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawGroupsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawGroupsClient
        """
        return self._raw_client

    def list(
        self,
        agent_id: str,
        *,
        manager_type: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Group]:
        """
        Lists the groups for an agent

        Parameters
        ----------
        agent_id : str

        manager_type : typing.Optional[str]
            Manager type to filter groups by

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Group]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.agents.groups.list(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.list(agent_id, manager_type=manager_type, request_options=request_options)
        return _response.data


class AsyncGroupsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawGroupsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawGroupsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawGroupsClient
        """
        return self._raw_client

    async def list(
        self,
        agent_id: str,
        *,
        manager_type: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Group]:
        """
        Lists the groups for an agent

        Parameters
        ----------
        agent_id : str

        manager_type : typing.Optional[str]
            Manager type to filter groups by

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Group]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.agents.groups.list(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(agent_id, manager_type=manager_type, request_options=request_options)
        return _response.data
