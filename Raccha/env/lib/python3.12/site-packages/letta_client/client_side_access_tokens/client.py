# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import <PERSON><PERSON><PERSON><PERSON><PERSON>rap<PERSON>, Sync<PERSON><PERSON>Wrapper
from ..core.request_options import RequestOptions
from .raw_client import AsyncRawClientSideAccessTokensClient, RawClientSideAccessTokensClient
from .types.client_side_access_tokens_create_request_policy_item import ClientSideAccessTokensCreateRequestPolicyItem
from .types.client_side_access_tokens_create_response import Client<PERSON>ideAccessTokensCreateResponse
from .types.client_side_access_tokens_list_client_side_access_tokens_response import (
    ClientSideAccessTokensListClientSideAccessTokensResponse,
)

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ClientSideAccessTokensClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawClientSideAccessTokensClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawClientSideAccessTokensClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawClientSideAccessTokensClient
        """
        return self._raw_client

    def client_side_access_tokens_list_client_side_access_tokens(
        self,
        *,
        agent_id: typing.Optional[str] = None,
        offset: typing.Optional[float] = None,
        limit: typing.Optional[float] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ClientSideAccessTokensListClientSideAccessTokensResponse:
        """
        List all client side access tokens for the current account. This is only available for cloud users.

        Parameters
        ----------
        agent_id : typing.Optional[str]
            The agent ID to filter tokens by. If provided, only tokens for this agent will be returned.

        offset : typing.Optional[float]
            The offset for pagination. Defaults to 0.

        limit : typing.Optional[float]
            The number of tokens to return per page. Defaults to 10.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ClientSideAccessTokensListClientSideAccessTokensResponse
            200

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.client_side_access_tokens.client_side_access_tokens_list_client_side_access_tokens()
        """
        _response = self._raw_client.client_side_access_tokens_list_client_side_access_tokens(
            agent_id=agent_id, offset=offset, limit=limit, request_options=request_options
        )
        return _response.data

    def create(
        self,
        *,
        policy: typing.Sequence[ClientSideAccessTokensCreateRequestPolicyItem],
        hostname: str,
        expires_at: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ClientSideAccessTokensCreateResponse:
        """
        Create a new client side access token with the specified configuration.

        Parameters
        ----------
        policy : typing.Sequence[ClientSideAccessTokensCreateRequestPolicyItem]

        hostname : str
            The hostname of the client side application. Please specify the full URL including the protocol (http or https).

        expires_at : typing.Optional[str]
            The expiration date of the token. If not provided, the token will expire in 5 minutes

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ClientSideAccessTokensCreateResponse
            201

        Examples
        --------
        from letta_client import Letta
        from letta_client.client_side_access_tokens import (
            ClientSideAccessTokensCreateRequestPolicyItem,
        )

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.client_side_access_tokens.create(
            policy=[
                ClientSideAccessTokensCreateRequestPolicyItem(
                    id="id",
                    access=["read_messages"],
                )
            ],
            hostname="hostname",
        )
        """
        _response = self._raw_client.create(
            policy=policy, hostname=hostname, expires_at=expires_at, request_options=request_options
        )
        return _response.data

    def delete(
        self,
        token: str,
        *,
        request: typing.Optional[typing.Any] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Delete a client side access token.

        Parameters
        ----------
        token : str
            The access token to delete

        request : typing.Optional[typing.Any]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            204

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.client_side_access_tokens.delete(
            token="token",
            request={"key": "value"},
        )
        """
        _response = self._raw_client.delete(token, request=request, request_options=request_options)
        return _response.data


class AsyncClientSideAccessTokensClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawClientSideAccessTokensClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawClientSideAccessTokensClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawClientSideAccessTokensClient
        """
        return self._raw_client

    async def client_side_access_tokens_list_client_side_access_tokens(
        self,
        *,
        agent_id: typing.Optional[str] = None,
        offset: typing.Optional[float] = None,
        limit: typing.Optional[float] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ClientSideAccessTokensListClientSideAccessTokensResponse:
        """
        List all client side access tokens for the current account. This is only available for cloud users.

        Parameters
        ----------
        agent_id : typing.Optional[str]
            The agent ID to filter tokens by. If provided, only tokens for this agent will be returned.

        offset : typing.Optional[float]
            The offset for pagination. Defaults to 0.

        limit : typing.Optional[float]
            The number of tokens to return per page. Defaults to 10.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ClientSideAccessTokensListClientSideAccessTokensResponse
            200

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.client_side_access_tokens.client_side_access_tokens_list_client_side_access_tokens()


        asyncio.run(main())
        """
        _response = await self._raw_client.client_side_access_tokens_list_client_side_access_tokens(
            agent_id=agent_id, offset=offset, limit=limit, request_options=request_options
        )
        return _response.data

    async def create(
        self,
        *,
        policy: typing.Sequence[ClientSideAccessTokensCreateRequestPolicyItem],
        hostname: str,
        expires_at: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ClientSideAccessTokensCreateResponse:
        """
        Create a new client side access token with the specified configuration.

        Parameters
        ----------
        policy : typing.Sequence[ClientSideAccessTokensCreateRequestPolicyItem]

        hostname : str
            The hostname of the client side application. Please specify the full URL including the protocol (http or https).

        expires_at : typing.Optional[str]
            The expiration date of the token. If not provided, the token will expire in 5 minutes

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ClientSideAccessTokensCreateResponse
            201

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta
        from letta_client.client_side_access_tokens import (
            ClientSideAccessTokensCreateRequestPolicyItem,
        )

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.client_side_access_tokens.create(
                policy=[
                    ClientSideAccessTokensCreateRequestPolicyItem(
                        id="id",
                        access=["read_messages"],
                    )
                ],
                hostname="hostname",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            policy=policy, hostname=hostname, expires_at=expires_at, request_options=request_options
        )
        return _response.data

    async def delete(
        self,
        token: str,
        *,
        request: typing.Optional[typing.Any] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Delete a client side access token.

        Parameters
        ----------
        token : str
            The access token to delete

        request : typing.Optional[typing.Any]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            204

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.client_side_access_tokens.delete(
                token="token",
                request={"key": "value"},
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(token, request=request, request_options=request_options)
        return _response.data
