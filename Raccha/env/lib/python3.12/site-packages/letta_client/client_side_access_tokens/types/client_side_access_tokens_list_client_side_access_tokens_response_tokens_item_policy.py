# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ...core.pydantic_utilities import IS_PYDANTIC_V2
from ...core.unchecked_base_model import UncheckedBaseModel
from .client_side_access_tokens_list_client_side_access_tokens_response_tokens_item_policy_data_item import (
    ClientSideAccessTokensListClientSideAccessTokensResponseTokensItemPolicyDataItem,
)


class ClientSideAccessTokensListClientSideAccessTokensResponseTokensItemPolicy(UncheckedBaseModel):
    version: typing.Literal["1"] = "1"
    data: typing.List[ClientSideAccessTokensListClientSideAccessTokensResponseTokensItemPolicyDataItem]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
