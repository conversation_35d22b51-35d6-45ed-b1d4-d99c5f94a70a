# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ...core.pydantic_utilities import IS_PYDANTIC_V2
from ...core.unchecked_base_model import UncheckedBaseModel
from .client_side_access_tokens_create_request_policy_item_access_item import (
    ClientSideAccessTokensCreateRequestPolicyItemAccessItem,
)


class ClientSideAccessTokensCreateRequestPolicyItem(UncheckedBaseModel):
    type: typing.Literal["agent"] = "agent"
    id: str
    access: typing.List[ClientSideAccessTokensCreateRequestPolicyItemAccessItem]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
