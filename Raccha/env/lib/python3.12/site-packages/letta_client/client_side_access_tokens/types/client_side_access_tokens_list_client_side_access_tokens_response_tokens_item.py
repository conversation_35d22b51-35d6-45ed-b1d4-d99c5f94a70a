# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
import typing_extensions
from ...core.pydantic_utilities import IS_PYDANTIC_V2
from ...core.serialization import FieldMetadata
from ...core.unchecked_base_model import UncheckedBaseModel
from .client_side_access_tokens_list_client_side_access_tokens_response_tokens_item_policy import (
    ClientSideAccessTokensListClientSideAccessTokensResponseTokensItemPolicy,
)


class ClientSideAccessTokensListClientSideAccessTokensResponseTokensItem(UncheckedBaseModel):
    policy: ClientSideAccessTokensListClientSideAccessTokensResponseTokensItemPolicy
    token: str
    hostname: str
    expires_at: typing_extensions.Annotated[str, FieldMetadata(alias="expiresAt")]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
