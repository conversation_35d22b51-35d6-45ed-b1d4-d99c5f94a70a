# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
import typing_extensions
from ...core.pydantic_utilities import IS_PYDANTIC_V2
from ...core.serialization import FieldMetadata
from ...core.unchecked_base_model import UncheckedBaseModel
from .client_side_access_tokens_create_response_policy import ClientSideAccessTokensCreateResponsePolicy


class ClientSideAccessTokensCreateResponse(UncheckedBaseModel):
    policy: ClientSideAccessTokensCreateResponsePolicy
    token: str
    hostname: str
    expires_at: typing_extensions.Annotated[str, FieldMetadata(alias="expiresAt")]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
