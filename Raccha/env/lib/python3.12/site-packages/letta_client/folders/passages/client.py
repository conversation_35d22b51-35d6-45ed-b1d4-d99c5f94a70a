# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.passage import Passage
from .raw_client import AsyncRawPassagesClient, RawPassagesClient


class PassagesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawPassagesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawPassagesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawPassagesClient
        """
        return self._raw_client

    def list(
        self,
        folder_id: str,
        *,
        after: typing.Optional[str] = None,
        before: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Passage]:
        """
        List all passages associated with a data folder.

        Parameters
        ----------
        folder_id : str

        after : typing.Optional[str]
            Message after which to retrieve the returned messages.

        before : typing.Optional[str]
            Message before which to retrieve the returned messages.

        limit : typing.Optional[int]
            Maximum number of messages to retrieve.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Passage]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.passages.list(
            folder_id="folder_id",
        )
        """
        _response = self._raw_client.list(
            folder_id, after=after, before=before, limit=limit, request_options=request_options
        )
        return _response.data


class AsyncPassagesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawPassagesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawPassagesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawPassagesClient
        """
        return self._raw_client

    async def list(
        self,
        folder_id: str,
        *,
        after: typing.Optional[str] = None,
        before: typing.Optional[str] = None,
        limit: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[Passage]:
        """
        List all passages associated with a data folder.

        Parameters
        ----------
        folder_id : str

        after : typing.Optional[str]
            Message after which to retrieve the returned messages.

        before : typing.Optional[str]
            Message before which to retrieve the returned messages.

        limit : typing.Optional[int]
            Maximum number of messages to retrieve.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Passage]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.passages.list(
                folder_id="folder_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            folder_id, after=after, before=before, limit=limit, request_options=request_options
        )
        return _response.data
