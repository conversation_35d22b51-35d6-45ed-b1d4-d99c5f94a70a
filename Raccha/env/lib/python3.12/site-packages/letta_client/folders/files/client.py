# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ... import core
from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.duplicate_file_handling import DuplicateFileHandling
from ...types.file_metadata import FileMetadata
from .raw_client import Async<PERSON><PERSON><PERSON><PERSON><PERSON>lient, RawFilesClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class FilesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawFilesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawFilesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawFilesClient
        """
        return self._raw_client

    def upload(
        self,
        folder_id: str,
        *,
        file: core.File,
        duplicate_handling: typing.Optional[DuplicateFileHandling] = None,
        name: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> FileMetadata:
        """
        Upload a file to a data folder.

        Parameters
        ----------
        folder_id : str

        file : core.File
            See core.File for more documentation

        duplicate_handling : typing.Optional[DuplicateFileHandling]
            How to handle duplicate filenames

        name : typing.Optional[str]
            Optional custom name to override the uploaded file's name

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        FileMetadata
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.files.upload(
            folder_id="folder_id",
        )
        """
        _response = self._raw_client.upload(
            folder_id, file=file, duplicate_handling=duplicate_handling, name=name, request_options=request_options
        )
        return _response.data

    def list(
        self,
        folder_id: str,
        *,
        limit: typing.Optional[int] = None,
        after: typing.Optional[str] = None,
        include_content: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[FileMetadata]:
        """
        List paginated files associated with a data folder.

        Parameters
        ----------
        folder_id : str

        limit : typing.Optional[int]
            Number of files to return

        after : typing.Optional[str]
            Pagination cursor to fetch the next set of results

        include_content : typing.Optional[bool]
            Whether to include full file content

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[FileMetadata]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.files.list(
            folder_id="folder_id",
        )
        """
        _response = self._raw_client.list(
            folder_id, limit=limit, after=after, include_content=include_content, request_options=request_options
        )
        return _response.data

    def delete(self, folder_id: str, file_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Delete a file from a folder.

        Parameters
        ----------
        folder_id : str

        file_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.files.delete(
            folder_id="folder_id",
            file_id="file_id",
        )
        """
        _response = self._raw_client.delete(folder_id, file_id, request_options=request_options)
        return _response.data


class AsyncFilesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawFilesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawFilesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawFilesClient
        """
        return self._raw_client

    async def upload(
        self,
        folder_id: str,
        *,
        file: core.File,
        duplicate_handling: typing.Optional[DuplicateFileHandling] = None,
        name: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> FileMetadata:
        """
        Upload a file to a data folder.

        Parameters
        ----------
        folder_id : str

        file : core.File
            See core.File for more documentation

        duplicate_handling : typing.Optional[DuplicateFileHandling]
            How to handle duplicate filenames

        name : typing.Optional[str]
            Optional custom name to override the uploaded file's name

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        FileMetadata
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.files.upload(
                folder_id="folder_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.upload(
            folder_id, file=file, duplicate_handling=duplicate_handling, name=name, request_options=request_options
        )
        return _response.data

    async def list(
        self,
        folder_id: str,
        *,
        limit: typing.Optional[int] = None,
        after: typing.Optional[str] = None,
        include_content: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[FileMetadata]:
        """
        List paginated files associated with a data folder.

        Parameters
        ----------
        folder_id : str

        limit : typing.Optional[int]
            Number of files to return

        after : typing.Optional[str]
            Pagination cursor to fetch the next set of results

        include_content : typing.Optional[bool]
            Whether to include full file content

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[FileMetadata]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.files.list(
                folder_id="folder_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            folder_id, limit=limit, after=after, include_content=include_content, request_options=request_options
        )
        return _response.data

    async def delete(
        self, folder_id: str, file_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> None:
        """
        Delete a file from a folder.

        Parameters
        ----------
        folder_id : str

        file_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.files.delete(
                folder_id="folder_id",
                file_id="file_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(folder_id, file_id, request_options=request_options)
        return _response.data
