# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.embedding_config import EmbeddingConfig
from ..types.folder import Folder
from ..types.organization_sources_stats import OrganizationSourcesStats
from .files.client import Async<PERSON><PERSON><PERSON>lient, FilesClient
from .passages.client import Async<PERSON><PERSON>ages<PERSON>lient, PassagesClient
from .raw_client import AsyncRawFoldersClient, RawFoldersClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class FoldersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawFoldersClient(client_wrapper=client_wrapper)
        self.files = FilesClient(client_wrapper=client_wrapper)

        self.passages = PassagesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawFoldersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawFoldersClient
        """
        return self._raw_client

    def count(self, *, request_options: typing.Optional[RequestOptions] = None) -> int:
        """
        Count all data folders created by a user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        int
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.count()
        """
        _response = self._raw_client.count(request_options=request_options)
        return _response.data

    def retrieve(self, folder_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> Folder:
        """
        Get a folder by ID

        Parameters
        ----------
        folder_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Folder
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.retrieve(
            folder_id="folder_id",
        )
        """
        _response = self._raw_client.retrieve(folder_id, request_options=request_options)
        return _response.data

    def delete(
        self, folder_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a data folder.

        Parameters
        ----------
        folder_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.delete(
            folder_id="folder_id",
        )
        """
        _response = self._raw_client.delete(folder_id, request_options=request_options)
        return _response.data

    def modify(
        self,
        folder_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        instructions: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Folder:
        """
        Update the name or documentation of an existing data folder.

        Parameters
        ----------
        folder_id : str

        name : typing.Optional[str]
            The name of the source.

        description : typing.Optional[str]
            The description of the source.

        instructions : typing.Optional[str]
            Instructions for how to use the source.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata associated with the source.

        embedding_config : typing.Optional[EmbeddingConfig]
            The embedding configuration used by the source.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Folder
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.modify(
            folder_id="folder_id",
        )
        """
        _response = self._raw_client.modify(
            folder_id,
            name=name,
            description=description,
            instructions=instructions,
            metadata=metadata,
            embedding_config=embedding_config,
            request_options=request_options,
        )
        return _response.data

    def retrieve_by_name(self, folder_name: str, *, request_options: typing.Optional[RequestOptions] = None) -> str:
        """
        Get a folder by name

        Parameters
        ----------
        folder_name : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        str
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.retrieve_by_name(
            folder_name="folder_name",
        )
        """
        _response = self._raw_client.retrieve_by_name(folder_name, request_options=request_options)
        return _response.data

    def get_folders_metadata(
        self,
        *,
        include_detailed_per_source_metadata: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> OrganizationSourcesStats:
        """
        Get aggregated metadata for all folders in an organization.

        Returns structured metadata including:
        - Total number of folders
        - Total number of files across all folders
        - Total size of all files
        - Per-source breakdown with file details (file_name, file_size per file) if include_detailed_per_source_metadata is True

        Parameters
        ----------
        include_detailed_per_source_metadata : typing.Optional[bool]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        OrganizationSourcesStats
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.get_folders_metadata()
        """
        _response = self._raw_client.get_folders_metadata(
            include_detailed_per_source_metadata=include_detailed_per_source_metadata, request_options=request_options
        )
        return _response.data

    def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> typing.List[Folder]:
        """
        List all data folders created by a user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Folder]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.list()
        """
        _response = self._raw_client.list(request_options=request_options)
        return _response.data

    def create(
        self,
        *,
        name: str,
        description: typing.Optional[str] = OMIT,
        instructions: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        embedding: typing.Optional[str] = OMIT,
        embedding_chunk_size: typing.Optional[int] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Folder:
        """
        Create a new data folder.

        Parameters
        ----------
        name : str
            The name of the source.

        description : typing.Optional[str]
            The description of the source.

        instructions : typing.Optional[str]
            Instructions for how to use the source.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata associated with the source.

        embedding : typing.Optional[str]
            The handle for the embedding config used by the source.

        embedding_chunk_size : typing.Optional[int]
            The chunk size of the embedding.

        embedding_config : typing.Optional[EmbeddingConfig]
            (Legacy) The embedding configuration used by the source.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Folder
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.create(
            name="name",
        )
        """
        _response = self._raw_client.create(
            name=name,
            description=description,
            instructions=instructions,
            metadata=metadata,
            embedding=embedding,
            embedding_chunk_size=embedding_chunk_size,
            embedding_config=embedding_config,
            request_options=request_options,
        )
        return _response.data

    def get_agents_for_folder(
        self, folder_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[str]:
        """
        Get all agent IDs that have the specified folder attached.

        Parameters
        ----------
        folder_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[str]
            Successful Response

        Examples
        --------
        from letta_client import Letta

        client = Letta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )
        client.folders.get_agents_for_folder(
            folder_id="folder_id",
        )
        """
        _response = self._raw_client.get_agents_for_folder(folder_id, request_options=request_options)
        return _response.data


class AsyncFoldersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawFoldersClient(client_wrapper=client_wrapper)
        self.files = AsyncFilesClient(client_wrapper=client_wrapper)

        self.passages = AsyncPassagesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawFoldersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawFoldersClient
        """
        return self._raw_client

    async def count(self, *, request_options: typing.Optional[RequestOptions] = None) -> int:
        """
        Count all data folders created by a user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        int
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.count()


        asyncio.run(main())
        """
        _response = await self._raw_client.count(request_options=request_options)
        return _response.data

    async def retrieve(self, folder_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> Folder:
        """
        Get a folder by ID

        Parameters
        ----------
        folder_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Folder
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.retrieve(
                folder_id="folder_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.retrieve(folder_id, request_options=request_options)
        return _response.data

    async def delete(
        self, folder_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a data folder.

        Parameters
        ----------
        folder_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.delete(
                folder_id="folder_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(folder_id, request_options=request_options)
        return _response.data

    async def modify(
        self,
        folder_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        instructions: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Folder:
        """
        Update the name or documentation of an existing data folder.

        Parameters
        ----------
        folder_id : str

        name : typing.Optional[str]
            The name of the source.

        description : typing.Optional[str]
            The description of the source.

        instructions : typing.Optional[str]
            Instructions for how to use the source.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata associated with the source.

        embedding_config : typing.Optional[EmbeddingConfig]
            The embedding configuration used by the source.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Folder
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.modify(
                folder_id="folder_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.modify(
            folder_id,
            name=name,
            description=description,
            instructions=instructions,
            metadata=metadata,
            embedding_config=embedding_config,
            request_options=request_options,
        )
        return _response.data

    async def retrieve_by_name(
        self, folder_name: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> str:
        """
        Get a folder by name

        Parameters
        ----------
        folder_name : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        str
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.retrieve_by_name(
                folder_name="folder_name",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.retrieve_by_name(folder_name, request_options=request_options)
        return _response.data

    async def get_folders_metadata(
        self,
        *,
        include_detailed_per_source_metadata: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> OrganizationSourcesStats:
        """
        Get aggregated metadata for all folders in an organization.

        Returns structured metadata including:
        - Total number of folders
        - Total number of files across all folders
        - Total size of all files
        - Per-source breakdown with file details (file_name, file_size per file) if include_detailed_per_source_metadata is True

        Parameters
        ----------
        include_detailed_per_source_metadata : typing.Optional[bool]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        OrganizationSourcesStats
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.get_folders_metadata()


        asyncio.run(main())
        """
        _response = await self._raw_client.get_folders_metadata(
            include_detailed_per_source_metadata=include_detailed_per_source_metadata, request_options=request_options
        )
        return _response.data

    async def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> typing.List[Folder]:
        """
        List all data folders created by a user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[Folder]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(request_options=request_options)
        return _response.data

    async def create(
        self,
        *,
        name: str,
        description: typing.Optional[str] = OMIT,
        instructions: typing.Optional[str] = OMIT,
        metadata: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        embedding: typing.Optional[str] = OMIT,
        embedding_chunk_size: typing.Optional[int] = OMIT,
        embedding_config: typing.Optional[EmbeddingConfig] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Folder:
        """
        Create a new data folder.

        Parameters
        ----------
        name : str
            The name of the source.

        description : typing.Optional[str]
            The description of the source.

        instructions : typing.Optional[str]
            Instructions for how to use the source.

        metadata : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Metadata associated with the source.

        embedding : typing.Optional[str]
            The handle for the embedding config used by the source.

        embedding_chunk_size : typing.Optional[int]
            The chunk size of the embedding.

        embedding_config : typing.Optional[EmbeddingConfig]
            (Legacy) The embedding configuration used by the source.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Folder
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.create(
                name="name",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            name=name,
            description=description,
            instructions=instructions,
            metadata=metadata,
            embedding=embedding,
            embedding_chunk_size=embedding_chunk_size,
            embedding_config=embedding_config,
            request_options=request_options,
        )
        return _response.data

    async def get_agents_for_folder(
        self, folder_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[str]:
        """
        Get all agent IDs that have the specified folder attached.

        Parameters
        ----------
        folder_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[str]
            Successful Response

        Examples
        --------
        import asyncio

        from letta_client import AsyncLetta

        client = AsyncLetta(
            project="YOUR_PROJECT",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.folders.get_agents_for_folder(
                folder_id="folder_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get_agents_for_folder(folder_id, request_options=request_options)
        return _response.data
