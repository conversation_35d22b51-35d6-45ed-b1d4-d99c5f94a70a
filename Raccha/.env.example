# Raccha Development Environment Configuration

# Letta API Configuration
LETTA_BASE_URL=https://letta.raccha.ai
LETTA_TOKEN=product-raccha.ai

# Database Configuration
POSTGRES_DB=raccha_dev
POSTGRES_USER=raccha_user
POSTGRES_PASSWORD=raccha_dev_password
DATABASE_URL=**********************************************************/raccha_dev

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# Application Configuration
FLASK_ENV=development
FASTAPI_ENV=development
LOG_LEVEL=INFO
PYTHONUNBUFFERED=1

# Pool Configuration
POOL_TAG=pool:continuia_health

# Security (Change these in production!)
SECRET_KEY=dev-secret-key-change-in-production
JWT_SECRET=dev-jwt-secret-change-in-production

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000
