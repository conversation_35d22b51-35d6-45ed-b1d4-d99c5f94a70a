# Cookie-Based Session Management Implementation Guide

## Overview
This guide documents the new cookie-based session management system implemented to prevent conversation contamination between users. Each user session is now isolated using a secure session token that must be sent as a cookie or query parameter.

## Problem Solved
Previously, conversations could contaminate across users because session IDs were not properly isolated. The new system ensures:
- Each user gets a unique session token on session creation
- All API calls validate the token before processing
- Each session has its own isolated agent instance
- No cross-contamination between user conversations

## API Changes

### 1. Session Creation
**Endpoint:** `GET /agents/{agent_name}`

**Response:**
```json
{
  "session_id": "abc123",
  "session_token": "secure_token_xyz789"  // NEW: Use this for authentication
}
```

**Client Implementation:**
```javascript
// Create a new session
const response = await fetch('/agents/arika_reddy?category=health_care');
const data = await response.json();

// IMPORTANT: Store the session_token as a cookie
document.cookie = `session_token=${data.session_token}; path=/; SameSite=Strict; Secure`;

// Also store session_id for reference
localStorage.setItem('session_id', data.session_id);
```

### 2. WebSocket Connection
**Endpoint:** `/agents/ws`

**Parameters:**
- `agent_name`: Required (query param)
- `session_token`: Required (cookie or query param)

**Client Implementation:**
```javascript
// Option 1: Pass token as query parameter
const sessionToken = getCookie('session_token');
const ws = new WebSocket(`ws://localhost:8000/agents/ws?agent_name=arika_reddy&session_token=${sessionToken}`);

// Option 2: Ensure cookie is sent (if same domain)
const ws = new WebSocket('ws://localhost:8000/agents/ws?agent_name=arika_reddy');
// Browser will automatically send cookies

ws.onopen = () => {
  console.log('Connected with session isolation');
};

ws.onerror = (error) => {
  // Handle invalid token errors
  if (error.code === 1008) {
    // Session token invalid, create new session
    createNewSession();
  }
};
```

### 3. Getting Messages
**Endpoint:** `GET /agents/{agent_name}/messages`

**Authentication Methods (in order of preference):**
1. Cookie: `session_token` cookie
2. Query param: `?session_token=xxx`
3. Legacy: `?session_id=xxx` (deprecated, less secure)

**Client Implementation:**
```javascript
// Option 1: With cookie (automatic if same domain)
const response = await fetch('/agents/arika_reddy/messages', {
  credentials: 'include'  // Include cookies
});

// Option 2: With query parameter
const sessionToken = getCookie('session_token');
const response = await fetch(`/agents/arika_reddy/messages?session_token=${sessionToken}`);

const data = await response.json();
if (response.status === 401) {
  // Invalid token, create new session
  createNewSession();
}
```

### 4. Deleting Sessions
**Endpoint:** `DELETE /sessions`

**Client Implementation:**
```javascript
// Delete session on logout
async function logout() {
  const sessionToken = getCookie('session_token');
  
  await fetch('/sessions', {
    method: 'DELETE',
    headers: {
      'Cookie': `session_token=${sessionToken}`
    }
  });
  
  // Clear local storage and cookies
  document.cookie = 'session_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
  localStorage.removeItem('session_id');
}
```

## Cookie Management Utilities

```javascript
// Helper function to get cookie value
function getCookie(name) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
  return null;
}

// Helper function to set secure cookie
function setSessionCookie(token) {
  const secure = window.location.protocol === 'https:' ? 'Secure;' : '';
  document.cookie = `session_token=${token}; path=/; SameSite=Strict; ${secure}`;
}

// Helper function to clear session
function clearSession() {
  document.cookie = 'session_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
  localStorage.removeItem('session_id');
}
```

## Security Best Practices

1. **Always use HTTPS in production** to prevent token interception
2. **Set SameSite=Strict** to prevent CSRF attacks
3. **Use Secure flag** for HTTPS connections
4. **Clear tokens on logout** to prevent session hijacking
5. **Handle token errors** by creating new sessions

## Error Handling

### Invalid Token Errors
When a token is invalid or expired, the API will return:
- WebSocket: Close code 1008 with reason "Invalid session token"
- HTTP: Status 401 with error message

**Handle these by:**
1. Clearing the invalid token
2. Creating a new session
3. Informing the user if necessary

### Example Error Handler
```javascript
async function apiCall(url, options = {}) {
  const response = await fetch(url, {
    ...options,
    credentials: 'include'
  });
  
  if (response.status === 401) {
    // Token invalid, create new session
    clearSession();
    const newSession = await createNewSession();
    setSessionCookie(newSession.session_token);
    
    // Retry the call with new token
    return apiCall(url, options);
  }
  
  return response;
}
```

## Migration Guide

### For Existing Implementations
1. Update session creation to store the `session_token`
2. Modify WebSocket connections to include token
3. Update API calls to include token via cookie or query param
4. Add error handling for invalid tokens
5. Implement session cleanup on logout

### Testing Session Isolation
1. Open two browser windows (use incognito for one)
2. Create sessions in both windows
3. Send messages in each window
4. Verify conversations remain separate
5. Check server logs for unique agent_ids per session

## Server-Side Improvements

The implementation includes:
- Unique `agent_id` per session for complete isolation
- Token validation on all endpoints
- Session tracking with token mapping
- Detailed logging for debugging isolation issues
- Automatic cleanup of invalid sessions

## Troubleshooting

### Conversation Contamination Still Occurring
1. Verify cookies are being set correctly
2. Check browser developer tools for cookie presence
3. Ensure tokens are unique per user/browser
4. Review server logs for session isolation messages

### WebSocket Connection Failures
1. Check token is being passed correctly
2. Verify token hasn't been invalidated
3. Ensure cookies are enabled in browser
4. Check CORS settings if cross-origin

### Messages Not Loading
1. Verify session token is valid
2. Check session still exists on server
3. Ensure proper credentials in fetch requests
4. Review network tab for 401 errors

## Summary

The cookie-based session management ensures:
- **User Isolation**: Each user has a unique session token
- **Conversation Privacy**: No message contamination between users
- **Security**: Tokens are validated on every request
- **Scalability**: Sessions are properly tracked and managed

By following this guide, your client implementation will properly handle session isolation and prevent any conversation contamination between users.