-- PostgreSQL initialization script for Raccha Development
-- This script runs when the PostgreSQL container starts for the first time

-- Create additional databases if needed
CREATE DATABASE raccha_test;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE raccha_dev TO raccha_user;
GRANT ALL PRIVILEGES ON DATABASE raccha_test TO raccha_user;

-- Create basic tables for session and agent management
\c raccha_dev;

CREATE TABLE IF NOT EXISTS sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    agent_name VARCHAR(255),
    group_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active B<PERSON><PERSON>EAN DEFAULT TRUE
);

CREATE TABLE IF NOT EXISTS agent_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) REFERENCES sessions(session_id),
    agent_id VARCHAR(255),
    agent_name VARCHAR(255),
    role VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sessions_session_id ON sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_sessions_active ON sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_agent_sessions_session_id ON agent_sessions(session_id);

-- Insert some sample data for development
INSERT INTO sessions (session_id, agent_name, group_id) VALUES 
('dev-session-1', 'TestAgent', 'group-dev-1'),
('dev-session-2', 'HealthAgent', 'group-dev-2')
ON CONFLICT (session_id) DO NOTHING;
