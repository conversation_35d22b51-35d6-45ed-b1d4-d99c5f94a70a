import os
from typing import Optional

class Settings:
    """Simple settings class for configuration"""
    
    def __init__(self):
        # Base directory (project root inside container)
        self.BASE_DIR = os.getenv("BASE_DIR", "/app")
        
        # Letta settings
        self.LETTA_BASE_URL = os.getenv("LETTA_BASE_URL", "https://letta.raccha.ai").rstrip("/")
        self.LETTA_TOKEN = os.getenv("LETTA_TOKEN", "")
        
        # Server settings  
        self.PORT = int(os.getenv("PORT", "3000"))
        self.HOST = os.getenv("HOST", "0.0.0.0")
        
        # Docker-compatible paths
        self.TEMPLATES_DIR = os.getenv("TEMPLATES_DIR", os.path.join(self.BASE_DIR, "data", "case_management"))
        self.FOLDERS_BASE_DIR = os.getenv("FOLDERS_BASE_DIR", os.path.join(self.BASE_DIR, "data", "folders"))
        
        # Agent settings
        self.POOL_TAG = os.getenv("POOL_TAG", "pool:continuia_health")
        
        # Validate required settings
        self._validate()
        
    
    def get_template_agent_name(self, url_agent_name: str) -> str:
        """Convert URL-friendly agent name to template agent name"""
        # Agent name mappings for URL-friendly names
        agent_mappings = {
            "Arika_Reddy": "arika_reddy",      # URL name -> template name
            "arika_reddy": "arika_reddy",      # Allow both formats
            "medical_intake": "medical_intake",
            "clinical_coordinator": "clinical_coordinator"
        }
        
        mapped_name = agent_mappings.get(url_agent_name, url_agent_name.lower())
        print(f"Agent name mapping: '{url_agent_name}' -> '{mapped_name}'")
        return mapped_name
    
    def get_template_category_name(self, url_category: str) -> str:
        """Convert URL-friendly category name to template category name"""
        # Category name mappings
        category_mappings = {
            "health_care": "health_care",
            "healthcare": "health_care",       # Allow both naming conventions
            "medical": "health_care",
            "legal": "legal",
            "finance": "finance"
        }
        
        mapped_category = category_mappings.get(url_category, url_category)
        print(f"Category name mapping: '{url_category}' -> '{mapped_category}'")
        return mapped_category
    
    def _validate(self):
        """Validate required settings"""
        if not self.LETTA_BASE_URL:
            raise ValueError("LETTA_BASE_URL environment variable is required")
        if not self.LETTA_TOKEN:
            raise ValueError("LETTA_TOKEN environment variable is required")
        
        print(f"Config - BASE Directory: {self.BASE_DIR}")
        print(f"Config - Templates Directory: {self.TEMPLATES_DIR}")
        print(f"Config - Folders Base Directory: {self.FOLDERS_BASE_DIR}")
        
        # Check if templates directory exists
        if not os.path.exists(self.TEMPLATES_DIR):
            print(f"Warning: Templates directory not found: {self.TEMPLATES_DIR}")
            print("Available directories in /app/data:")
            data_dir = os.path.join(self.BASE_DIR, "data")
            if os.path.exists(data_dir):
                for item in os.listdir(data_dir):
                    item_path = os.path.join(data_dir, item)
                    print(f"  - {item} ({'dir' if os.path.isdir(item_path) else 'file'})")
            else:
                print(f"  {data_dir} does not exist")
        
        # Check if shared memory directory exists
        shared_memory_dir = os.path.join(os.path.dirname(self.TEMPLATES_DIR), "shared_memory")
        if not os.path.exists(shared_memory_dir):
            print(f"Warning: Shared memory directory not found: {shared_memory_dir}")
    
# Global settings instance
_settings = None

def get_settings() -> Settings:
    """Get settings singleton"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings