version: '3.8'

services:
  raccha-app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: raccha-dev
    user: "0:0" # Run as root inside container
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot reload
      - .:/app
      # Mount logs directory
      - ./logs:/app/logs
      # Mount data directory for persistence
      - ./data:/app/data
      # Exclude Python cache and virtual env from host
      - /app/__pycache__
      - /app/.pytest_cache
      - /app/env
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - LETTA_BASE_URL=${LETTA_BASE_URL:-https://letta.raccha.ai}
      - LETTA_TOKEN=${LETTA_TOKEN:-product-raccha.ai}
      - FLASK_ENV=development
      - FASTAPI_ENV=development
      - LOG_LEVEL=INFO
    networks:
      - raccha-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
      - postgres

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: raccha-redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - raccha-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 30s
      timeout: 3s
      retries: 3

  # PostgreSQL for persistent data storage
  postgres:
    image: postgres:15-alpine
    container_name: raccha-postgres
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=raccha_dev
      - POSTGRES_USER=raccha_user
      - POSTGRES_PASSWORD=raccha_dev_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - raccha-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U raccha_user -d raccha_dev" ]
      interval: 30s
      timeout: 5s
      retries: 3

  # Reddit MCP Server
  reddit-mcp:
    build:
      context: /root/my.continuia/mcp/reddit-mcp-server
      dockerfile: Dockerfile
    container_name: reddit-mcp-server
    ports:
      - "3005:3005"
    environment:
      - PYTHONUNBUFFERED=1
      - PORT=3005
    networks:
      - raccha-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3005/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local

networks:
  raccha-network:
    driver: bridge
    name: raccha-dev-network
