import os
import sys
import importlib.util

def register(client, logger):
    """Register both OTP tools with Letta"""
    
    try:
        # Get the path to the otp_verify.py file
        current_dir = os.path.dirname(__file__)
        otp_verify_path = os.path.join(current_dir, "otp_verify.py")
        
        # Load the module dynamically
        spec = importlib.util.spec_from_file_location("otp_verify", otp_verify_path)
        otp_verify_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(otp_verify_module)
        
        # Register verify_otp_code tool
        verify_otp_code = otp_verify_module.verify_otp_code
        import inspect
        import textwrap
        
        verify_source = textwrap.dedent(inspect.getsource(verify_otp_code))
        verify_tool = client.tools.upsert(
            source_code=verify_source,
            description="OTP verification tool for phone number validation",
            tags=["otp", "verification", "phone", "authentication", "healthcare"],
            source_type="python"
        )
        logger.info(f"✅ Registered verify_otp_code: {verify_tool.name} (ID: {verify_tool.id})")
        
        # Register send_otp tool
        send_otp = otp_verify_module.send_otp
        send_source = textwrap.dedent(inspect.getsource(send_otp))
        send_tool = client.tools.upsert(
            source_code=send_source,
            description="Send OTP code to phone number for verification",
            tags=["otp", "send", "phone", "authentication", "healthcare"],
            source_type="python"
        )
        logger.info(f"✅ Registered send_otp: {send_tool.name} (ID: {send_tool.id})")
        
        # Return the verify tool (primary tool)
        return verify_tool
        
    except Exception as e:
        logger.error(f"❌ Failed to register OTP tools: {e}")
        return None
