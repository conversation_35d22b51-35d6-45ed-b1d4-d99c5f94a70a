def register(client, logger):
    """
    Register OTP verification tool for Continuia Healthcare system.
    Only registers verify_otp_code function.
    
    Args:
        client: Letta client instance
        logger: Logger instance
        
    Returns:
        Tool object if successful, None otherwise
    """
    import sys
    import os
    import textwrap
    import inspect
    
    try:
        # Add the current directory to path to import otp_verify
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Import the otp_verify module
        import otp_verify as otp_verify_module
        
        # Register verify_otp_code tool only
        verify_otp_code = otp_verify_module.verify_otp_code
        verify_source = textwrap.dedent(inspect.getsource(verify_otp_code))
        verify_tool = client.tools.upsert(
            source_code=verify_source,
            description="Verify OTP code for phone number validation in Continuia Healthcare system",
            tags=["otp", "verification", "authentication", "healthcare", "continuia"],
            source_type="python"
        )
        logger.info(f"✅ Registered verify_otp_code: {verify_tool.name} (ID: {verify_tool.id})")
        
        return verify_tool
        
    except Exception as e:
        logger.error(f"❌ Failed to register OTP tools: {e}")
        return None
