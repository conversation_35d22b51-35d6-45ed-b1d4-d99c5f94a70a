def send_otp(phone_number: str, message_template: str = "verification") -> str:
    """
    Send OTP code to phone number for verification in Continuia Healthcare system.
    
    Args:
        phone_number (str): Phone number in format +91XXXXXXXXXX
        message_template (str): Template type for OTP message
        
    Returns:
        str: Success message with OTP code and instructions
    """
    import re
    import random
    from datetime import datetime
    
    # Validate phone number format
    if not phone_number:
        return "❌ Phone number is required."
    
    if not re.match(r'^\+91\d{10}$', phone_number):
        return "❌ Invalid phone number format. Please use +91XXXXXXXXXX format."
    
    # For demo purposes, always send the fixed OTP: 123123
    otp_code = "123123"
    timestamp = datetime.now().isoformat()
    
    return f"""✅ OTP Sent Successfully!

📱 **Verification Code: {otp_code}**

Sent to: {phone_number}
Time: {timestamp}

Please enter this 6-digit code to verify your phone number.

Note: For demo purposes, the OTP code is always 123123."""

def verify_otp_code(otp_code: str, phone_number: str = None) -> str:
    """
    Verify OTP code for phone number validation in Continuia Healthcare system.
    
    Args:
        otp_code (str): The 6-digit OTP code entered by user
        phone_number (str, optional): Phone number being verified
        
    Returns:
        str: Verification result with status and next steps
    """
    # Your existing verify_otp_code function stays exactly the same
    import re
    from datetime import datetime
    
    # Validate OTP format (must be exactly 6 digits)
    if not otp_code:
        return "❌ No OTP code provided. Please enter the 6-digit verification code."
    
    if not re.match(r'^\d{6}$', otp_code.strip()):
        return "❌ Invalid OTP format. Please enter exactly 6 digits (no spaces or other characters)."
    
    # Hardcoded valid OTP for the system
    VALID_OTP = "123123"
    
    if otp_code.strip() == VALID_OTP:
        # OTP verification successful
        timestamp = datetime.now().isoformat()
        
        return f"""✅ OTP Verification Successful!

Your phone number has been verified successfully.

📱 **Verification Details:**
• Code: {otp_code} ✓
• Phone: {phone_number if phone_number else '[verified]'} ✓  
• Verified at: {timestamp}
• Status: Authentication Complete

🔓 **You can now:**
• Upload medical documents and images
• Share detailed symptoms and medical history  
• Access your complete intake summary
• Continue with your healthcare consultation

What would you like to do next?"""
    
    else:
        # OTP verification failed
        return f"""❌ OTP Verification Failed

The code '{otp_code}' is incorrect. Please try again.

🔄 **Next Steps:**
• Check the 6-digit code sent to your phone
• Enter the correct verification code: 123123
• Make sure you enter exactly 6 digits with no spaces

Please enter the correct 6-digit OTP code."""
