def add_numbers(a: float, b: float) -> str:
    """
    Add two numbers together and return the result.
    
    This is a simple calculator tool that adds two numbers for Continuia Health system.
    
    Args:
        a (float): The first number to add
        b (float): The second number to add
        
    Returns:
        str: The sum of the two numbers with a friendly message
    """
    result = a + b
    return f"The sum of {a} and {b} is {result}. This calculation was done using Raccha.ai's custom addition tool!"