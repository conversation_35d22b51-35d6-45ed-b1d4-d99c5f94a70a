import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(__file__))

def register(client, logger):
    """Register the addition tool with Letta client"""
    try:
        # Import the function from addition.py
        from addition import add_numbers
        
        # Check if tool already exists
        existing_tools = client.tools.list()
        for tool in existing_tools:
            if hasattr(tool, 'name') and tool.name == 'add_numbers':
                logger.info(f"✅ Found existing add_numbers tool with ID: {tool.id}")
                return tool
        
        # Create the tool using the function
        tool = client.tools.create_from_function(
            func=add_numbers,
            description="Add two numbers together using a custom Raccha.ai tool for Continuia Health system",
            tags=["math", "calculator", "addition", "raccha.ai", "continuia", "custom"]
        )
        
        logger.info(f"✅ Successfully registered add_numbers tool with ID: {tool.id}")
        return tool
        
    except Exception as e:
        logger.error(f"❌ Failed to register add_numbers tool: {e}")
        return None