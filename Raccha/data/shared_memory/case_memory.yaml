memory_block:
  name: "case_memory"
  label: "case_memory"
  description: "All case details and information that user provides about their case"
  value: |
    case_id: null
    case_status: "new"
    priority_level: "standard"
    auth_timestamp: null
    intake_complete: false
    
    # All case details user tells
    chief_complaint: null
    symptom_description: null
    symptom_onset: null
    symptom_duration: null
    severity_level: null
    pain_location: null
    associated_symptoms: []
    
    # Medical episode details
    current_episode:
      triggers: []
      previous_treatment: []
      improvement_factors: []
      worsening_factors: []
      progression: null
    
    # Medical history user provides
    current_medications: []
    allergies: []
    chronic_conditions: []
    previous_surgeries: []
    family_history: []
    
    # Additional case information
    emergency_contacts: []
    insurance_information: null
    previous_healthcare_visits: []
    uploaded_medical_documents: []
    
  tags: ["case", "shared", "intake_agent", "case_id:{case_id}", "user_id:{user_id}"]