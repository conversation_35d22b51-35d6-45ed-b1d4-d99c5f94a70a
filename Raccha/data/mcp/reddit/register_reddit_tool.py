from letta_client import Letta

client = Letta(
    base_url="https://letta.raccha.ai/v1",
    token="product-raccha.ai"
)

# Step 1: Register MCP server (one time only)
server = client.tools.add_mcp_server(
    server_name="reddit_mcp",
    url="https://reddit.raccha.ai/mcp",  # container DNS (works inside docker network)
    description="Reddit MCP Server - Standalone"
)
print("✅ MCP server registered:", server.server_name)

# Step 2: Register reddit_search tool
tool = client.tools.add_mcp_tool(
    mcp_server_name="reddit_mcp",
    mcp_tool_name="reddit_search"
)
print("✅ MCP tool registered:", tool.name, tool.id)
