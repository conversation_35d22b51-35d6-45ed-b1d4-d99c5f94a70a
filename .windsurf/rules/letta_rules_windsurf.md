# Letta-Client SDK Documentation

## Overview

The letta-client SDK is a Python package that provides a comprehensive interface for interacting with the Letta platform. This SDK enables the creation and management of AI agents, memory blocks, tools, and other resources within the Letta ecosystem.

## Installation

The letta-client SDK is installed in the project's virtual environment. To use it, ensure the virtual environment is activated:

```bash
source venv/bin/activate
```

## Core Modules

### 1. Client Module

The client module provides the main interface for interacting with the Letta API.

#### Key Classes:
- `Letta`: Main client class for synchronous operations
- `AsyncLetta`: Client class for asynchronous operations
- `ToolsClient`: Client for working with tools
- `AsyncToolsClient`: Asynchronous client for working with tools

#### Usage:
```python
import letta_client

# Access the client directly
client = letta_client.client

# Make API calls
agents = client.agents.list()
```

### 2. Agents Module

The agents module provides functionality for creating, managing, and interacting with AI agents.

#### Key Components:
- Agent creation and management
- Agent search capabilities
- Memory management for agents
- Message handling
- Tool integration

#### Sub-modules:
- `blocks`: Manage memory blocks for agents
- `messages`: Handle agent messages
- `tools`: Manage tools for agents
- `sources`: Manage knowledge sources for agents
- `templates`: Work with agent templates

#### Usage:
```python
import letta_client

# List all agents
agents = letta_client.agents.list()

# Create a new agent
new_agent = letta_client.agents.create(
    name="Healthcare Assistant",
    description="An agent that assists with healthcare workflows",
    # Additional parameters as needed
)

# Get a specific agent
agent = letta_client.agents.get("agent_id")

# Delete an agent
letta_client.agents.delete("agent_id")
```

### 3. Blocks Module

The blocks module provides functionality for managing memory blocks, which store information for agents.

#### Key Operations:
- Create memory blocks
- Retrieve memory blocks
- Update memory blocks
- Delete memory blocks

#### Usage:
```python
import letta_client

# List all blocks
blocks = letta_client.blocks.list()

# Create a new memory block
new_block = letta_client.blocks.create(
    name="Patient Information",
    content="Patient demographic and history information",
    # Additional parameters as needed
)

# Get a specific block
block = letta_client.blocks.get("block_id")

# Delete a block
letta_client.blocks.delete("block_id")
```

### 4. Tools Module

The tools module provides functionality for managing tools that agents can use.

#### Key Components:
- MCP (Model Control Protocol) server management
- Tool creation and management
- Tool execution

#### Usage:
```python
import letta_client

# List all tools
tools = letta_client.tools.list()

# Create a new tool
new_tool = letta_client.tools.create(
    name="Healthcare Data Lookup",
    description="A tool for looking up healthcare data",
    # Additional parameters as needed
)

# Get a specific tool
tool = letta_client.tools.get("tool_id")

# Delete a tool
letta_client.tools.delete("tool_id")
```

### 5. Messages Module

The messages module provides functionality for managing messages between agents and users.

#### Usage:
```python
import letta_client

# List messages for an agent
messages = letta_client.messages.list(agent_id="agent_id")

# Create a new message
new_message = letta_client.messages.create(
    agent_id="agent_id",
    content="Hello, how can I help you?",
    role="assistant",
    # Additional parameters as needed
)
```

### 6. Sources Module

The sources module provides functionality for managing knowledge sources for agents.

#### Sub-modules:
- `files`: Manage file sources
- `passages`: Manage text passages

#### Usage:
```python
import letta_client

# List all sources
sources = letta_client.sources.list()

# Create a new source
new_source = letta_client.sources.create(
    name="Medical Knowledge Base",
    description="A knowledge base for medical information",
    # Additional parameters as needed
)

# Get a specific source
source = letta_client.sources.get("source_id")

# Delete a source
letta_client.sources.delete("source_id")
```

## Common Data Types

### Agent

Represents an AI agent in the Letta platform.

#### Properties:
- `id`: Unique identifier for the agent
- `name`: Name of the agent
- `description`: Description of the agent
- `created_at`: Timestamp when the agent was created
- `updated_at`: Timestamp when the agent was last updated

### Block

Represents a memory block in the Letta platform.

#### Properties:
- `id`: Unique identifier for the block
- `name`: Name of the block
- `content`: Content of the block
- `created_at`: Timestamp when the block was created
- `updated_at`: Timestamp when the block was last updated

### Tool

Represents a tool that agents can use.

#### Properties:
- `id`: Unique identifier for the tool
- `name`: Name of the tool
- `description`: Description of the tool
- `created_at`: Timestamp when the tool was created
- `updated_at`: Timestamp when the tool was last updated

### Message

Represents a message between an agent and a user.

#### Properties:
- `id`: Unique identifier for the message
- `agent_id`: ID of the agent associated with the message
- `content`: Content of the message
- `role`: Role of the message sender (e.g., "user", "assistant")
- `created_at`: Timestamp when the message was created

## Healthcare Agent Implementation

The letta-client SDK is used in this project to implement healthcare agents that handle various workflows:

1. **Case Handling**: Patient intake, case creation, routing, monitoring, and resolution
2. **Doctor Review Process**: Medical assessment, treatment planning, specialist consultation, and documentation
3. **Patient Questionnaire System**: Dynamic questionnaires, response processing, risk assessment, and follow-up scheduling
4. **Multi-Party Chat Capabilities**: Agent-to-agent communication, patient-agent interaction, provider coordination, and language support

## Cleanup Utilities

The project includes several cleanup utilities that use the letta-client SDK:

1. **cleanup_all_agents.py**: Deletes all healthcare agents and memory blocks from the Letta server
2. **complete_letta_cleanup.py**: Comprehensive cleanup script that clears all Letta resources
3. **working_letta_cleanup.py**: Uses the actual letta_client API structure to clear all resources

### Usage:
```python
# Import the cleanup utility
from cleanup_all_agents import HealthcareAgentsCleanup

# Create an instance with optional parameters
cleanup = HealthcareAgentsCleanup(dry_run=True, verbose=True)

# Initialize the connection to the Letta server
cleanup.initialize_factory()

# Run the cleanup
cleanup.cleanup_all_agents()
cleanup.cleanup_all_blocks()
```

## Best Practices

1. **Resource Management**: Always clean up resources when they are no longer needed
2. **Error Handling**: Implement proper error handling for API calls
3. **Dry Run Mode**: Use dry run mode for cleanup operations to preview changes before executing them
4. **Verbose Logging**: Enable verbose logging for detailed information about operations

## Troubleshooting

1. **Connection Issues**: Ensure the Letta server is running and accessible
2. **Authentication Errors**: Verify that authentication credentials are correct
3. **Resource Not Found**: Check that resource IDs are correct and resources exist
4. **API Errors**: Review error messages for specific API issues

## Additional Resources

For more information about the Letta platform and SDK, refer to the official documentation.
